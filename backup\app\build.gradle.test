plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.appystore.mrecharge'
    compileSdk 35

    defaultConfig {
        applicationId "com.appystore.mrecharge"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
        // Enable core library desugaring
        coreLibraryDesugaringEnabled true
    }
}

dependencies {
    // Core library desugaring
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.3'

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout

    // Retrofit for network requests
    implementation libs.retrofit
    implementation libs.converter.gson
    implementation libs.okhttp
    implementation libs.logging.interceptor

    // Firebase and Google Play Services
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.messaging
    implementation libs.play.services.base

    // WorkManager for background tasks
    implementation libs.work.runtime

    // Volley for network requests
    implementation libs.volley
    
    // Temporarily comment out the ads-mobile-sdk to see if it's causing issues
    // implementation libs.ads.mobile.sdk

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}
