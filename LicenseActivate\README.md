# License Activation App with Approval Workflow

This Android application implements a comprehensive approval workflow for API authentication requests, integrating with the existing admin panel at `http://*************/AppyStoreMRecharge/admin/`.

## Features Implemented

### 1. **Android App (LicenseActivate)**
- **Professional UI**: Clean, modern interface with license key and PIN input fields
- **Authentication Flow**: Secure API communication with the server
- **Approval Handling**: Proper handling of pending, approved, and rejected states
- **Automatic Polling**: Checks approval status every 10 seconds for up to 5 minutes
- **Persistent State**: Remembers pending approvals across app restarts
- **Error Handling**: Comprehensive error handling and user feedback

### 2. **API Enhancement (device_auth.php)**
- **New Endpoint**: Added `action=check_approval_status` for polling approval status
- **Enhanced Response**: Returns complete license details when approved
- **Status Codes**:
  - `status: 1` - Approved (with license details)
  - `status: 2` - Pending approval
  - `status: 3` - Rejected
  - `status: 0` - Error/Not found

### 3. **Admin Panel Integration**
- **Existing Approval System**: Leverages the existing approval workflow in dashboard.php
- **Manual Approval**: <PERSON>mins can approve/reject requests through the web interface
- **Real-time Updates**: App automatically detects approval status changes

## Workflow Process

### 1. **Initial Authentication Request**
1. User enters license key and PIN in the Android app
2. App sends authentication request to the API
3. API validates license and creates auth_logs entry with `approval_status = 'pending'`
4. API returns `status: 2` with log_id for pending approval

### 2. **Pending State**
1. App shows "Approval Pending" dialog
2. Starts automatic polling every 10 seconds
3. User can manually check status or wait for automatic updates
4. Status text shows progress: "Still pending approval... (X/30)"

### 3. **Admin Approval**
1. Admin logs into the web panel at `http://*************/AppyStoreMRecharge/admin/`
2. Views pending requests in the dashboard
3. Clicks approve/reject buttons for each request
4. Database is updated with approval decision

### 4. **Approval Detection**
1. App polls the API with `action=check_approval_status&log_id=X`
2. API returns current status:
   - **Approved**: Returns full license details and activates app
   - **Rejected**: Shows rejection message and stops polling
   - **Still Pending**: Continues polling

### 5. **Final State**
- **If Approved**: App activates, saves settings, and shows success message
- **If Rejected**: Shows rejection message and allows retry
- **If Timeout**: Stops polling after 5 minutes, user can retry manually

## Technical Implementation

### Android App Components
- **MainActivity.java**: Main activity with authentication logic
- **Professional UI**: CardView-based layout with modern design
- **Network Security**: Configured for HTTP connections to local server
- **Permissions**: Internet and network state permissions

### API Enhancements
- **New Endpoint**: `check_approval_status` action in device_auth.php
- **Backward Compatibility**: Maintains existing `check_approval` endpoint
- **Enhanced Responses**: Returns complete license data when approved

### Database Schema
The existing `auth_logs` table includes:
- `approval_status`: 'pending', 'approved', 'rejected'
- `approved_at`: Timestamp of approval decision
- `log_id`: Unique identifier for tracking requests

## Testing

### Demo Licenses Available
The system includes demo licenses for testing:
- **License Key**: `DEMO-123456`, **PIN**: `1234`
- **License Key**: `DEMO-654321`, **PIN**: `4321`
- **License Key**: `DEMO-111111`, **PIN**: `1111`

Demo licenses are auto-approved for immediate testing.

### Test Workflow
1. Use a real license key (not DEMO-*) to trigger approval workflow
2. Check admin panel for pending requests
3. Approve/reject through web interface
4. Verify app receives updates automatically

## Configuration

### API URL
Default: `http://*************/AppyStoreMRecharge/admin/api/device_auth.php`

### Polling Settings
- **Interval**: 10 seconds
- **Max Attempts**: 30 (5 minutes total)
- **Auto-start**: Resumes on app restart if pending

### Network Security
- Configured for HTTP connections to local development server
- Allows cleartext traffic for testing environment

## Security Considerations

1. **HTTPS**: Should be enabled for production deployment
2. **Rate Limiting**: Consider implementing rate limiting for approval checks
3. **Authentication**: Admin panel requires login for approval actions
4. **Validation**: All inputs are validated and sanitized

## License Expiration Management System

### **🕒 Real-time Expiration Display**
- **Prominent Display**: License expiration date and countdown prominently shown in main interface
- **Live Countdown**: Real-time updates showing days, hours, and minutes remaining
- **Color Coding**:
  - 🟢 **Green**: More than 2 days remaining (normal)
  - 🟠 **Orange**: 2-48 hours remaining (warning period)
  - 🔴 **Red**: Less than 24 hours remaining (critical)
- **Progress Bar**: Visual indicator of time remaining relative to total license period

### **⚠️ Pre-expiration Warning System**
- **Warning Period**: Begins 48 hours (2 days) before expiration
- **Notification Frequency**: Every 5 minutes during warning period
- **Dual Notifications**: Both in-app dialogs and system notifications
- **Escalating Urgency**: Different styles for 2-day warnings vs final 24 hours
- **Non-dismissible**: Final 24-hour warnings cannot be dismissed

### **🚪 Automatic Logout on Expiration**
- **Immediate Logout**: Automatic logout when license reaches exact expiration time
- **Data Clearing**: All authentication data and preferences cleared
- **Redirect**: Automatic redirect to license activation screen
- **Clear Messaging**: Explicit explanation that license has expired

### **🔧 Technical Implementation**

#### **Expiration Monitoring**
- **Background Monitoring**: Continues even when app is minimized
- **Timezone Handling**: Proper timezone difference management
- **Server Sync**: Expiration date synced from server during authentication
- **Local Persistence**: Expiration date stored locally for offline checking
- **Real-time Updates**: Countdown updates every minute

#### **Notification System**
- **System Notifications**: High-priority notifications with custom styling
- **In-app Dialogs**: Modal dialogs with renewal options
- **Notification Channel**: Dedicated channel for license expiration warnings
- **Visual/Audio Alerts**: LED lights, vibration, and sound for critical warnings

#### **UI/UX Features**
- **Dedicated Section**: Expiration status prominently displayed in main screen
- **Visual Indicators**: Progress bars and color-coded status
- **Renewal Options**: Easy access to license renewal process
- **Professional Design**: Clean, modern interface with clear messaging

### **🧪 Testing the Expiration System**

#### **Test Licenses Available**
Use the test script at `http://*************/AppyStoreMRecharge/admin/test_expiration.php`

1. **Already Expired**: `TEST-EXPIRED` / PIN: `1111`
   - Should immediately show expiration dialog and logout
2. **Expiring Soon**: `TEST-EXPIRING-SOON` / PIN: `2222`
   - Shows red warnings every 5 minutes, non-dismissible dialogs
3. **Warning Period**: `TEST-WARNING-PERIOD` / PIN: `3333`
   - Shows orange warnings every 5 minutes
4. **Valid License**: `TEST-VALID-LONG` / PIN: `4444`
   - Shows green status, no warnings

#### **Testing Scenarios**
1. **Immediate Expiration**: Use test script to set license to expire in 1-5 minutes
2. **Warning Period**: Test 48-hour warning period functionality
3. **Critical Period**: Test final 24-hour non-dismissible warnings
4. **Background Monitoring**: Minimize app and verify notifications continue
5. **Automatic Logout**: Verify automatic logout occurs at exact expiration time

### **📱 User Experience Flow**

#### **Normal Operation (>2 days remaining)**
1. Green expiration status displayed
2. Countdown shows days/hours remaining
3. Progress bar shows healthy status
4. No warnings or interruptions

#### **Warning Period (2 days - 24 hours)**
1. Orange expiration status
2. Warning notifications every 5 minutes
3. In-app dialogs with "Renew License" option
4. System notifications with renewal actions
5. Dismissible warnings with "Remind Later" option

#### **Critical Period (< 24 hours)**
1. Red expiration status
2. Urgent notifications every 5 minutes
3. Non-dismissible warning dialogs
4. Persistent system notifications
5. Only "Renew License" option available

#### **Expiration Event**
1. Automatic logout triggered
2. All authentication data cleared
3. Expiration notification sent
4. Redirect to activation screen
5. Clear expiration message displayed

### **🔒 Security Considerations**
- **Server Validation**: Expiration dates validated against server when possible
- **Tamper Protection**: Local expiration data protected from manipulation
- **Secure Logout**: Complete data clearing on expiration
- **Audit Trail**: All expiration events logged for security monitoring

## Future Enhancements

1. **Push Notifications**: Real-time approval notifications
2. **Batch Operations**: Bulk approve/reject functionality
3. **Approval Reasons**: Add reason field for rejections
4. **Auto-expiry**: Automatic cleanup of old pending requests
5. **Analytics**: Track approval rates and response times
6. **Grace Period**: Optional grace period after expiration
7. **Renewal Integration**: Direct integration with license renewal system
8. **Usage Analytics**: Track app usage patterns before expiration
