<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Internet permission for API calls -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Network state permission to check connectivity -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Notification permissions for real-time notifications and expiration warnings -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Telecom recharge permissions -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- Additional permissions for enhanced notification functionality -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- Recharge module permissions -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- Telecom permissions for dual-SIM support -->
    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />

    <!-- Background service permissions -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Appy99Lisence"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".DashboardActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainActivity" />
        </activity>

        <!-- Recharge Module Activities -->
        <activity
            android:name=".recharge.RechargeActivity"
            android:exported="false"
            android:parentActivityName=".DashboardActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".DashboardActivity" />
        </activity>

        <activity
            android:name=".recharge.RechargeHistoryActivity"
            android:exported="false"
            android:parentActivityName=".DashboardActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".DashboardActivity" />
        </activity>

        <activity
            android:name=".recharge.RechargeSettingsActivity"
            android:exported="false"
            android:parentActivityName=".DashboardActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".DashboardActivity" />
        </activity>



        <!-- Recharge Background Service -->
        <service
            android:name=".recharge.RechargeService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall|dataSync" />

        <!-- SMS Receiver for recharge responses -->
        <receiver
            android:name=".recharge.RechargeSmsReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
            </intent-filter>
        </receiver>

        <!-- Telecom Services -->
        <service
            android:name=".telecom.TelecomService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall|dataSync" />

        <!-- Telecom SMS Receiver -->
        <receiver
            android:name=".telecom.TelecomSmsReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
            </intent-filter>
        </receiver>

        <!-- Telecom USSD Accessibility Service -->
        <service
            android:name=".telecom.TelecomUSSDService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="false">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/telecom_accessibility_service_config" />
        </service>

    </application>

</manifest>