<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Order ID and Status Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/orderIdText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Order: RCH_123456"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#2c3e50" />

            <TextView
                android:id="@+id/statusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Completed"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#27ae60"
                android:background="#d5f4e6"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:radius="4dp" />

        </LinearLayout>

        <!-- Phone Number and Amount Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/phoneNumberText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📞 +8801712345678"
                android:textSize="14sp"
                android:textColor="#34495e" />

            <TextView
                android:id="@+id/amountText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💰 50 BDT"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#e74c3c" />

        </LinearLayout>

        <!-- Operator and Date Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/operatorText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📡 Grameenphone"
                android:textSize="12sp"
                android:textColor="#7f8c8d" />

            <TextView
                android:id="@+id/dateText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📅 2024-01-15 10:30"
                android:textSize="12sp"
                android:textColor="#7f8c8d" />

        </LinearLayout>

        <!-- SMS Response (if available) -->
        <TextView
            android:id="@+id/smsResponseText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="SMS: Recharge successful"
            android:textSize="11sp"
            android:textColor="#95a5a6"
            android:layout_marginTop="8dp"
            android:background="#f8f9fa"
            android:padding="8dp"
            android:radius="4dp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
