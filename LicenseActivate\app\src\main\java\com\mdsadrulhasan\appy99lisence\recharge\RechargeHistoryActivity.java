package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.appy99lisence.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Activity to display recharge transaction history
 */
public class RechargeHistoryActivity extends AppCompatActivity {
    
    private static final String TAG = "RechargeHistoryActivity";
    
    // UI Components
    private RecyclerView historyRecyclerView;
    private TextView statusText;
    private But<PERSON> backButton;
    private Button refreshButton;
    
    // Data
    private String licenseKey;
    private String deviceId;
    private SharedPreferences preferences;
    private RechargeDbHelper dbHelper;
    private RechargeApiClient apiClient;
    
    // Adapter
    private RechargeHistoryAdapter historyAdapter;
    private List<RechargeTransaction> transactions;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recharge_history);
        
        // Initialize components
        initializeComponents();
        
        // Get intent data
        extractIntentData();
        
        // Initialize UI
        initializeUI();
        
        // Load history
        loadRechargeHistory();
        
        Log.d(TAG, "RechargeHistoryActivity created");
    }
    
    private void initializeComponents() {
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        dbHelper = new RechargeDbHelper(this);
        apiClient = new RechargeApiClient(this);
        transactions = new ArrayList<>();
    }
    
    private void extractIntentData() {
        Intent intent = getIntent();
        licenseKey = intent.getStringExtra("license_key");
        deviceId = intent.getStringExtra("device_id");
        
        // Fallback to preferences if not in intent
        if (licenseKey == null) licenseKey = preferences.getString("license_key", "");
        if (deviceId == null) deviceId = preferences.getString("device_id", "");
        
        Log.d(TAG, "License: " + (licenseKey != null ? licenseKey.substring(0, Math.min(8, licenseKey.length())) + "..." : "null"));
    }
    
    private void initializeUI() {
        historyRecyclerView = findViewById(R.id.historyRecyclerView);
        statusText = findViewById(R.id.statusText);
        backButton = findViewById(R.id.backButton);
        refreshButton = findViewById(R.id.refreshButton);
        
        // Setup RecyclerView
        historyRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        historyAdapter = new RechargeHistoryAdapter(transactions);
        historyRecyclerView.setAdapter(historyAdapter);
        
        // Setup button listeners
        setupButtonListeners();
        
        // Update status
        updateStatus("Loading recharge history...");
    }
    
    private void setupButtonListeners() {
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        refreshButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadRechargeHistory();
            }
        });
    }
    
    private void loadRechargeHistory() {
        updateStatus("Loading recharge history...");
        
        // Load from local database first
        loadLocalHistory();
        
        // Then sync with server
        syncWithServer();
    }
    
    private void loadLocalHistory() {
        try {
            Cursor cursor = dbHelper.getRechargeHistory(100); // Get last 100 transactions
            
            transactions.clear();
            
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    RechargeTransaction transaction = new RechargeTransaction();
                    transaction.orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));
                    transaction.phoneNumber = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_PHONE_NUMBER));
                    transaction.amount = cursor.getDouble(cursor.getColumnIndex(RechargeDbHelper.COLUMN_AMOUNT));
                    transaction.operator = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_OPERATOR));
                    transaction.status = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_STATUS));
                    transaction.createdAt = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_CREATED_AT));
                    transaction.smsResponse = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SMS_RESPONSE));
                    
                    transactions.add(transaction);
                } while (cursor.moveToNext());
                
                cursor.close();
            }
            
            // Update UI
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    historyAdapter.notifyDataSetChanged();
                    updateStatus("Loaded " + transactions.size() + " transactions from local database");
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading local history", e);
            updateStatus("Error loading local history");
        }
    }
    
    private void syncWithServer() {
        // Sync with server to get latest data
        apiClient.getRechargeHistory(100, 0, new RechargeApiClient.RechargeCallback() {
            @Override
            public void onResponse(org.json.JSONObject response) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (response.getBoolean("success")) {
                                // Process server response and update local database
                                // This is a simplified implementation
                                updateStatus("Synced with server successfully");
                            } else {
                                updateStatus("Server sync failed: " + response.getString("message"));
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error processing server response", e);
                            updateStatus("Error processing server response");
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.e(TAG, "Server sync error: " + error);
                        updateStatus("Server sync error: " + error);
                    }
                });
            }
        });
    }
    
    private void updateStatus(String status) {
        if (statusText != null) {
            statusText.setText(status);
        }
        Log.d(TAG, "Status: " + status);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (apiClient != null) {
            apiClient.cleanup();
        }
        if (dbHelper != null) {
            dbHelper.close();
        }
    }
    
    /**
     * Data class for recharge transactions
     */
    public static class RechargeTransaction {
        public String orderId;
        public String phoneNumber;
        public double amount;
        public String operator;
        public String status;
        public String createdAt;
        public String smsResponse;
    }
    
    /**
     * Adapter for recharge history RecyclerView
     */
    public static class RechargeHistoryAdapter extends RecyclerView.Adapter<RechargeHistoryAdapter.ViewHolder> {
        
        private List<RechargeTransaction> transactions;
        
        public RechargeHistoryAdapter(List<RechargeTransaction> transactions) {
            this.transactions = transactions;
        }
        
        @Override
        public ViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            android.view.View view = android.view.LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_recharge_history, parent, false);
            return new ViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            RechargeTransaction transaction = transactions.get(position);
            
            holder.orderIdText.setText("Order: " + transaction.orderId);
            holder.phoneNumberText.setText("📞 " + transaction.phoneNumber);
            holder.amountText.setText("💰 " + transaction.amount + " BDT");
            holder.operatorText.setText("📡 " + transaction.operator);
            holder.statusText.setText("Status: " + transaction.status);
            holder.dateText.setText("📅 " + transaction.createdAt);
            
            // Set status color
            int statusColor;
            switch (transaction.status.toLowerCase()) {
                case "completed":
                    statusColor = android.graphics.Color.GREEN;
                    break;
                case "failed":
                    statusColor = android.graphics.Color.RED;
                    break;
                case "pending":
                    statusColor = android.graphics.Color.BLUE;
                    break;
                case "processing":
                    statusColor = android.graphics.Color.parseColor("#FF9800");
                    break;
                default:
                    statusColor = android.graphics.Color.GRAY;
                    break;
            }
            holder.statusText.setTextColor(statusColor);
        }
        
        @Override
        public int getItemCount() {
            return transactions.size();
        }
        
        public static class ViewHolder extends RecyclerView.ViewHolder {
            public TextView orderIdText;
            public TextView phoneNumberText;
            public TextView amountText;
            public TextView operatorText;
            public TextView statusText;
            public TextView dateText;
            
            public ViewHolder(View view) {
                super(view);
                orderIdText = view.findViewById(R.id.orderIdText);
                phoneNumberText = view.findViewById(R.id.phoneNumberText);
                amountText = view.findViewById(R.id.amountText);
                operatorText = view.findViewById(R.id.operatorText);
                statusText = view.findViewById(R.id.statusText);
                dateText = view.findViewById(R.id.dateText);
            }
        }
    }
}
