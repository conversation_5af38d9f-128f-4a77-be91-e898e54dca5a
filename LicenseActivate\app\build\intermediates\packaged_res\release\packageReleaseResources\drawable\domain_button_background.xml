<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#16a085" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Disabled state -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#95a5a6" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#1abc9c" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
