1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdsadrulhasan.appy99lisence"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Network state permission to check connectivity -->
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- Notification permissions for real-time notifications and expiration warnings -->
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:13:22-63
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:14:5-68
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:14:22-65
21
22    <!-- Telecom recharge permissions -->
23    <uses-permission android:name="android.permission.CALL_PHONE" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:5-69
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:22-66
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:5-75
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:22-72
25    <uses-permission android:name="android.permission.RECEIVE_SMS" />
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:5-70
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:22-67
26    <uses-permission android:name="android.permission.READ_SMS" />
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:5-67
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:22-64
27    <uses-permission android:name="android.permission.SEND_SMS" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:5-67
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:22-64
28    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22:5-85
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22:22-82
29    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:5-78
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:22-75
30    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:5-77
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:22-74
31    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:5-95
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:22-92
32
33    <!-- Additional permissions for enhanced notification functionality -->
34    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
34-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:28:5-81
34-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:28:22-78
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:5-77
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:22-74
36
37    <!-- Recharge module permissions -->
38    <uses-permission android:name="android.permission.CALL_PHONE" />
38-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:5-69
38-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:22-66
39    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
39-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:5-75
39-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:22-72
40    <uses-permission android:name="android.permission.RECEIVE_SMS" />
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:5-70
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:22-67
41    <uses-permission android:name="android.permission.READ_SMS" />
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:5-67
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:22-64
42    <uses-permission android:name="android.permission.SEND_SMS" />
42-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:5-67
42-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:22-64
43    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
43-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:5-78
43-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:22-75
44    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
44-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:5-95
44-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:22-92
45
46    <!-- Telecom permissions for dual-SIM support -->
47    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
47-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41:5-74
47-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41:22-71
48    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
48-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42:5-77
48-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42:22-74
49
50    <!-- Background service permissions -->
51    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
51-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:45:5-88
51-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:45:22-85
52    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:46:5-87
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:46:22-84
53
54    <permission
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
59
60    <application
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:48:5-156:19
61        android:allowBackup="true"
61-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:49:9-35
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
63        android:dataExtractionRules="@xml/data_extraction_rules"
63-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:50:9-65
64        android:debuggable="true"
65        android:extractNativeLibs="false"
66        android:fullBackupContent="@xml/backup_rules"
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:51:9-54
67        android:icon="@mipmap/ic_launcher"
67-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:52:9-43
68        android:label="@string/app_name"
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:53:9-41
69        android:networkSecurityConfig="@xml/network_security_config"
69-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:57:9-69
70        android:roundIcon="@mipmap/ic_launcher_round"
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:54:9-54
71        android:supportsRtl="true"
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:55:9-35
72        android:theme="@style/Theme.Appy99Lisence"
72-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:56:9-51
73        android:usesCleartextTraffic="true" >
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:58:9-44
74        <activity
74-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:60:9-68:20
75            android:name="com.mdsadrulhasan.appy99lisence.MainActivity"
75-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:61:13-41
76            android:exported="true" >
76-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:62:13-36
77            <intent-filter>
77-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:63:13-67:29
78                <action android:name="android.intent.action.MAIN" />
78-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:17-69
78-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:25-66
79
80                <category android:name="android.intent.category.LAUNCHER" />
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:66:17-77
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:66:27-74
81            </intent-filter>
82        </activity>
83        <activity
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:70:9-77:20
84            android:name="com.mdsadrulhasan.appy99lisence.DashboardActivity"
84-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:71:13-46
85            android:exported="false"
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:72:13-37
86            android:parentActivityName="com.mdsadrulhasan.appy99lisence.MainActivity" >
86-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:73:13-55
87            <meta-data
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
88                android:name="android.support.PARENT_ACTIVITY"
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
89                android:value=".MainActivity" />
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
90        </activity>
91
92        <!-- Recharge Module Activities -->
93        <activity
93-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:80:9-87:20
94            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity"
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:81:13-54
95            android:exported="false"
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:82:13-37
96            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:83:13-60
97            <meta-data
97-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
98                android:name="android.support.PARENT_ACTIVITY"
98-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
99                android:value=".DashboardActivity" />
99-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
100        </activity>
101        <activity
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:89:9-96:20
102            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity"
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:90:13-61
103            android:exported="false"
103-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:91:13-37
104            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
104-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:92:13-60
105            <meta-data
105-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
106                android:name="android.support.PARENT_ACTIVITY"
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
107                android:value=".DashboardActivity" />
107-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
108        </activity>
109        <activity
109-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:98:9-105:20
110            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity"
110-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:99:13-62
111            android:exported="false"
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:100:13-37
112            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
112-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:101:13-60
113            <meta-data
113-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
114                android:name="android.support.PARENT_ACTIVITY"
114-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
115                android:value=".DashboardActivity" />
115-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
116        </activity>
117
118        <!-- Recharge Background Service -->
119        <service
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:110:9-114:66
120            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeService"
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:111:13-53
121            android:enabled="true"
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:112:13-35
122            android:exported="false"
122-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:113:13-37
123            android:foregroundServiceType="phoneCall|dataSync" />
123-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:114:13-63
124
125        <!-- SMS Receiver for recharge responses -->
126        <receiver
126-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:117:9-124:20
127            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSmsReceiver"
127-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:118:13-57
128            android:enabled="true"
128-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:119:13-35
129            android:exported="true" >
129-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:120:13-36
130            <intent-filter android:priority="1000" >
130-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:13-123:29
130-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:28-51
131                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
131-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:17-82
131-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:25-79
132            </intent-filter>
133        </receiver>
134
135        <!-- Telecom Services -->
136        <service
136-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:127:9-131:66
137            android:name="com.mdsadrulhasan.appy99lisence.telecom.TelecomService"
137-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:128:13-51
138            android:enabled="true"
138-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:129:13-35
139            android:exported="false"
139-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:130:13-37
140            android:foregroundServiceType="phoneCall|dataSync" />
140-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:131:13-63
141
142        <!-- Telecom SMS Receiver -->
143        <receiver
143-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:134:9-141:20
144            android:name="com.mdsadrulhasan.appy99lisence.telecom.TelecomSmsReceiver"
144-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:135:13-55
145            android:enabled="true"
145-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:136:13-35
146            android:exported="true" >
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:137:13-36
147            <intent-filter android:priority="1000" >
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:13-123:29
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:28-51
148                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
148-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:17-82
148-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:25-79
149            </intent-filter>
150        </receiver>
151
152        <!-- Telecom USSD Accessibility Service -->
153        <service
153-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:144:9-154:19
154            android:name="com.mdsadrulhasan.appy99lisence.telecom.TelecomUSSDService"
154-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:145:13-55
155            android:exported="false"
155-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:147:13-37
156            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
156-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:146:13-79
157            <intent-filter>
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:148:13-150:29
158                <action android:name="android.accessibilityservice.AccessibilityService" />
158-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:149:17-92
158-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:149:25-89
159            </intent-filter>
160
161            <meta-data
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:151:13-153:80
162                android:name="android.accessibilityservice"
162-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:152:17-60
163                android:resource="@xml/telecom_accessibility_service_config" />
163-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:153:17-77
164        </service>
165
166        <provider
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
167            android:name="androidx.startup.InitializationProvider"
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
168            android:authorities="com.mdsadrulhasan.appy99lisence.androidx-startup"
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
169            android:exported="false" >
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
170            <meta-data
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
171                android:name="androidx.emoji2.text.EmojiCompatInitializer"
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
172                android:value="androidx.startup" />
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
173            <meta-data
173-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
174-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
175                android:value="androidx.startup" />
175-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
176            <meta-data
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
177-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
178                android:value="androidx.startup" />
178-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
179        </provider>
180
181        <uses-library
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
182            android:name="androidx.window.extensions"
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
183            android:required="false" />
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
184        <uses-library
184-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
185            android:name="androidx.window.sidecar"
185-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
186            android:required="false" />
186-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
187
188        <receiver
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
189            android:name="androidx.profileinstaller.ProfileInstallReceiver"
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
190            android:directBootAware="false"
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
191            android:enabled="true"
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
192            android:exported="true"
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
193            android:permission="android.permission.DUMP" >
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
195                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
196            </intent-filter>
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
198                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
201                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
204                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
205            </intent-filter>
206        </receiver>
207    </application>
208
209</manifest>
