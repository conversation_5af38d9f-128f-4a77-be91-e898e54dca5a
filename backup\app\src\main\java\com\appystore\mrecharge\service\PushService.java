package com.appystore.mrecharge.service;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.preference.PreferenceManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.activity.MainActivity;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import java.util.Map;

public class PushService extends FirebaseMessagingService {
    private static final String TAG = "PushService";
    private static final String CHANNEL_ID = "PushNotificationChannel";
    private static final int NOTIFICATION_ID = 3;

    @Override
    public void onMessageReceived(@NonNull RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        Log.d(TAG, "From: " + remoteMessage.getFrom());

        // Check if message contains a data payload
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());
            handleDataMessage(remoteMessage.getData());
        }

        // Check if message contains a notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
            sendNotification(
                    remoteMessage.getNotification().getTitle(),
                    remoteMessage.getNotification().getBody()
            );
        }
    }

    @Override
    public void onNewToken(@NonNull String token) {
        super.onNewToken(token);
        Log.d(TAG, "Refreshed token: " + token);

        // Save the token to shared preferences
        savePreference("fcm_token", token);

        // Send the token to your server
        sendRegistrationToServer(token);
    }

    private void handleDataMessage(Map<String, String> data) {
        String type = data.get("type");
        String message = data.get("message");
        String title = data.get("title");

        if (type != null) {
            switch (type) {
                case "recharge":
                    // Handle recharge request
                    String number = data.get("number");
                    String amount = data.get("amount");
                    String orderId = data.get("order_id");

                    if (number != null && amount != null && orderId != null) {
                        // Save to database for processing
                        DbHelper dbHelper = new DbHelper(getApplicationContext());
                        dbHelper.insertContact(
                                orderId, number, amount, data.get("ussd"), data.get("pcode"),
                                data.get("line") != null ? Integer.parseInt(data.get("line")) : 0,
                                data.get("first"), data.get("second"),
                                data.get("third"), data.get("fourth"), data.get("fifth"),
                                data.get("trigger") != null ? Integer.parseInt(data.get("trigger")) : 0,
                                data.get("slot") != null ? Integer.parseInt(data.get("slot")) : 0,
                                data.get("powerload") != null ? Integer.parseInt(data.get("powerload")) : 0,
                                data.get("status") != null ? Integer.parseInt(data.get("status")) : 0,
                                data.get("resend") != null ? Integer.parseInt(data.get("resend")) : 0
                        );
                    }
                    break;

                case "sms":
                    // Handle SMS request
                    String smsNumber = data.get("number");
                    String smsText = data.get("text");

                    if (smsNumber != null && smsText != null) {
                        // Start SMS service
                        Intent smsIntent = new Intent(this, Smsend.class);
                        smsIntent.putExtra("number", smsNumber);
                        smsIntent.putExtra("text", smsText);
                        startService(smsIntent);
                    }
                    break;

                case "update":
                    // Handle update notification
                    // You might want to show a notification to update the app
                    break;

                default:
                    // Unknown type, just show notification
                    sendNotification(title, message);
                    break;
            }
        } else {
            // No type specified, just show notification
            sendNotification(title, message);
        }
    }

    private void sendRegistrationToServer(String token) {
        // TODO: Implement this method to send token to your app server
        // This would typically involve a network request to your backend
    }

    private void sendNotification(String title, String messageBody) {
        if (title == null) {
            title = "AppyStore MRecharge";
        }

        if (messageBody == null) {
            return;
        }

        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        int flags = PendingIntent.FLAG_ONE_SHOT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }

        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent, flags);

        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(this, CHANNEL_ID)
                        .setSmallIcon(R.drawable.ic_stat_name)
                        .setContentTitle(title)
                        .setContentText(messageBody)
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent);

        NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Since android Oreo notification channel is needed
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Push Notification Channel",
                    NotificationManager.IMPORTANCE_DEFAULT);
            notificationManager.createNotificationChannel(channel);
        }

        notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build());
    }

    // Save string preference
    private void savePreference(String key, String value) {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        editor.putString(key, value);
        editor.apply();
    }
}
