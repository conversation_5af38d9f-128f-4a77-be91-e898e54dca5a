package com.mdsadrulhasan.appy99lisence.fragments;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.mdsadrulhasan.appy99lisence.R;

public class DashboardHomeFragment extends Fragment {

    private static final String TAG = "DashboardHomeFragment";

    // UI Components
    private TextView deviceIdText;
    private TextView serviceStatusText;
    private Button startServiceButton;
    private Button stopServiceButton;
    private TextView simAStatus;
    private TextView simBStatus;
    private ToggleButton smsServiceToggle;

    // Data from parent activity
    private String licenseKey;
    private String domainUrl;
    private boolean activationStatus;
    private long expirationTimestamp;
    private String deviceId;
    private String deviceInfo;

    private SharedPreferences preferences;

    public static DashboardHomeFragment newInstance(String licenseKey, String domainUrl,
                                                   boolean activationStatus, long expirationTimestamp,
                                                   String deviceId, String deviceInfo) {
        DashboardHomeFragment fragment = new DashboardHomeFragment();
        Bundle args = new Bundle();
        args.putString("license_key", licenseKey);
        args.putString("domain_url", domainUrl);
        args.putBoolean("activation_status", activationStatus);
        args.putLong("expiration_timestamp", expirationTimestamp);
        args.putString("device_id", deviceId);
        args.putString("device_info", deviceInfo);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            licenseKey = getArguments().getString("license_key");
            domainUrl = getArguments().getString("domain_url");
            activationStatus = getArguments().getBoolean("activation_status");
            expirationTimestamp = getArguments().getLong("expiration_timestamp");
            deviceId = getArguments().getString("device_id");
            deviceInfo = getArguments().getString("device_info");
        }

        preferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dashboard_home_content, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initializeViews(view);
        setupClickListeners();
        loadDashboardData();
    }

    private void initializeViews(View view) {
        deviceIdText = view.findViewById(R.id.device_id_text);
        serviceStatusText = view.findViewById(R.id.service_status_text);
        startServiceButton = view.findViewById(R.id.start_service_button);
        stopServiceButton = view.findViewById(R.id.stop_service_button);
        simAStatus = view.findViewById(R.id.sim_a_status);
        simBStatus = view.findViewById(R.id.sim_b_status);
        smsServiceToggle = view.findViewById(R.id.sms_service_toggle);
    }

    private void setupClickListeners() {
        if (startServiceButton != null) {
            startServiceButton.setOnClickListener(v -> startTelecomService());
        }

        if (stopServiceButton != null) {
            stopServiceButton.setOnClickListener(v -> stopTelecomService());
        }

        if (smsServiceToggle != null) {
            smsServiceToggle.setOnCheckedChangeListener((buttonView, isChecked) -> {
                preferences.edit().putBoolean("sms_service_enabled", isChecked).apply();
                updateServiceStatus();
            });
        }
    }

    private void loadDashboardData() {
        // Load device information
        loadDeviceInfo();

        // Load service status
        updateServiceStatus();

        // Load SIM information
        loadSimInfo();

        // Load SMS service setting
        loadSmsServiceSetting();
    }

    private void loadDeviceInfo() {
        if (deviceIdText != null) {
            String deviceId = preferences.getString("device_id", "Unknown");
            deviceIdText.setText(deviceId);
        }
    }

    private void updateServiceStatus() {
        if (serviceStatusText != null) {
            boolean telecomServiceRunning = preferences.getBoolean("telecom_service_running", false);
            boolean smsServiceEnabled = preferences.getBoolean("sms_service_enabled", true);

            String status;
            if (telecomServiceRunning && smsServiceEnabled) {
                status = "Service Status: Running";
                serviceStatusText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else if (telecomServiceRunning) {
                status = "Service Status: Partial (SMS disabled)";
                serviceStatusText.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            } else {
                status = "Service Status: Stopped";
                serviceStatusText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }

            serviceStatusText.setText(status);
        }
    }

    private void loadSimInfo() {
        if (simAStatus != null) {
            String simAInfo = preferences.getString("sim_a_info", "Item 1");
            simAStatus.setText(simAInfo);
        }

        if (simBStatus != null) {
            String simBInfo = preferences.getString("sim_b_info", "Item 1");
            simBStatus.setText(simBInfo);
        }
    }

    private void loadSmsServiceSetting() {
        if (smsServiceToggle != null) {
            boolean smsEnabled = preferences.getBoolean("sms_service_enabled", true);
            smsServiceToggle.setChecked(smsEnabled);
        }
    }

    private void startTelecomService() {
        try {
            // Update preference
            preferences.edit().putBoolean("telecom_service_running", true).apply();

            // Update UI
            updateServiceStatus();

            // Show toast
            Toast.makeText(requireContext(), "Telecom service started", Toast.LENGTH_SHORT).show();

            Log.d(TAG, "Telecom service started from home fragment");
        } catch (Exception e) {
            Log.e(TAG, "Error starting telecom service", e);
            Toast.makeText(requireContext(), "Error starting service", Toast.LENGTH_SHORT).show();
        }
    }

    private void stopTelecomService() {
        try {
            // Update preference
            preferences.edit().putBoolean("telecom_service_running", false).apply();

            // Update UI
            updateServiceStatus();

            // Show toast
            Toast.makeText(requireContext(), "Telecom service stopped", Toast.LENGTH_SHORT).show();

            Log.d(TAG, "Telecom service stopped from home fragment");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping telecom service", e);
            Toast.makeText(requireContext(), "Error stopping service", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh data when fragment becomes visible
        loadDashboardData();
    }
}
