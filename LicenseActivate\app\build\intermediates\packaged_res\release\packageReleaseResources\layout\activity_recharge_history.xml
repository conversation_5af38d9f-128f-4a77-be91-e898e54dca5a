<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f5f5f5"
    tools:context=".recharge.RechargeHistoryActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#3498db"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📊 Recharge History"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#ffffff" />

        <Button
            android:id="@+id/refreshButton"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="🔄 Refresh"
            android:textSize="12sp"
            android:textColor="#ffffff"
            android:background="@drawable/button_background"
            android:layout_marginEnd="8dp" />

    </LinearLayout>

    <!-- Status Text -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Loading recharge history..."
        android:textSize="14sp"
        android:textColor="#2c3e50"
        android:background="#ecf0f1"
        android:padding="12dp"
        android:gravity="center" />

    <!-- History RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/historyRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false"
        android:scrollbars="vertical" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📭"
            android:textSize="48sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No recharge history found"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#34495e"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Your recharge transactions will appear here"
            android:textSize="14sp"
            android:textColor="#7f8c8d"
            android:gravity="center" />

    </LinearLayout>

    <!-- Footer with Back Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#f5f5f5"
        android:padding="16dp">

        <Button
            android:id="@+id/backButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="🔙 Back to Dashboard"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#ffffff"
            android:background="@drawable/button_background"
            android:elevation="2dp" />

    </LinearLayout>

</LinearLayout>
