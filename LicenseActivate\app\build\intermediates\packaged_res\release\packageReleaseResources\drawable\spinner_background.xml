<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#f0f0f0"/>
            <stroke android:width="1dp" android:color="#cccccc"/>
            <corners android:radius="8dp"/>
            <padding android:left="12dp" android:top="12dp" android:right="12dp" android:bottom="12dp"/>
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#e8f4fd"/>
            <stroke android:width="2dp" android:color="#3498db"/>
            <corners android:radius="8dp"/>
            <padding android:left="12dp" android:top="12dp" android:right="12dp" android:bottom="12dp"/>
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#ffffff"/>
            <stroke android:width="2dp" android:color="#3498db"/>
            <corners android:radius="8dp"/>
            <padding android:left="12dp" android:top="12dp" android:right="12dp" android:bottom="12dp"/>
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#ffffff"/>
            <stroke android:width="1dp" android:color="#bdc3c7"/>
            <corners android:radius="8dp"/>
            <padding android:left="12dp" android:top="12dp" android:right="12dp" android:bottom="12dp"/>
        </shape>
    </item>
</selector>
