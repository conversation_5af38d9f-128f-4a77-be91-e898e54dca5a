{"logs": [{"outputFile": "com.mdsadrulhasan.appy99lisence.app-mergeReleaseResources-39:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e6f8e0c71d36565d9ba51a46700a63c5\\transformed\\material-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1043,1108,1197,1262,1321,1407,1471,1535,1598,1668,1732,1786,1891,1949,2011,2065,2137,2254,2341,2417,2509,2591,2674,2814,2891,2972,3099,3190,3267,3321,3372,3438,3508,3585,3656,3731,3802,3879,3948,4017,4124,4215,4287,4376,4465,4539,4611,4697,4747,4826,4892,4972,5056,5118,5182,5245,5314,5414,5509,5601,5693,5751,5806,5887,5968,6043", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "267,349,427,504,590,674,776,899,978,1038,1103,1192,1257,1316,1402,1466,1530,1593,1663,1727,1781,1886,1944,2006,2060,2132,2249,2336,2412,2504,2586,2669,2809,2886,2967,3094,3185,3262,3316,3367,3433,3503,3580,3651,3726,3797,3874,3943,4012,4119,4210,4282,4371,4460,4534,4606,4692,4742,4821,4887,4967,5051,5113,5177,5240,5309,5409,5504,5596,5688,5746,5801,5882,5963,6038,6113"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,4222,4324,4447,4603,4663,4812,4901,4966,5025,5111,5175,5239,5302,5372,5436,5490,5595,5653,5715,5769,5841,5958,6045,6121,6213,6295,6378,6518,6595,6676,6803,6894,6971,7025,7076,7142,7212,7289,7360,7435,7506,7583,7652,7721,7828,7919,7991,8080,8169,8243,8315,8401,8451,8530,8596,8676,8760,8822,8886,8949,9018,9118,9213,9305,9397,9455,9593,9900,9981,10056", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "317,3142,3220,3297,3383,3467,4319,4442,4521,4658,4723,4896,4961,5020,5106,5170,5234,5297,5367,5431,5485,5590,5648,5710,5764,5836,5953,6040,6116,6208,6290,6373,6513,6590,6671,6798,6889,6966,7020,7071,7137,7207,7284,7355,7430,7501,7578,7647,7716,7823,7914,7986,8075,8164,8238,8310,8396,8446,8525,8591,8671,8755,8817,8881,8944,9013,9113,9208,9300,9392,9450,9505,9669,9976,10051,10126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\857a9a6b52c9eeda6cd3464bddadc08a\\transformed\\appcompat-1.7.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,9817", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,9895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\45cfe67c7755d3e66160b5bcd91d999e\\transformed\\core-1.13.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3573,3676,3784,3889,3993,4093,10131", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3568,3671,3779,3884,3988,4088,4217,10227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25f7cfac3baf7533370fea96b4f461d8\\transformed\\preference-1.2.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,182,266,349,492,661,752", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "177,261,344,487,656,747,827"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4526,4728,9510,9674,10232,10401,10492", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "4598,4807,9588,9812,10396,10487,10567"}}]}]}