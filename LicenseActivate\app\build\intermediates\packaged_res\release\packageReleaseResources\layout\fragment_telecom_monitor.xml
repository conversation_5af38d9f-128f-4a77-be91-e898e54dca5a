<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Monitor Controls -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@drawable/card_background"
        android:elevation="4dp"
        android:radius="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Monitor Controls"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#3F51B5"
                android:layout_marginBottom="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="3">

                <Button
                    android:id="@+id/refresh_monitor_button"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    android:text="Refresh"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_background" />

                <Button
                    android:id="@+id/clear_logs_button"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:text="Clear Logs"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_background" />

                <Button
                    android:id="@+id/export_logs_button"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:text="Export"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_background" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Live Monitor -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/card_background"
        android:elevation="4dp"
        android:radius="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Live Monitor"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5" />

                <TextView
                    android:id="@+id/monitor_status_indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🟢 Active"
                    android:textSize="14sp"
                    android:textColor="#4CAF50" />
            </LinearLayout>

            <!-- Filter Options -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <Spinner
                    android:id="@+id/filter_spinner"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/input_background"
                    android:padding="8dp" />

                <ToggleButton
                    android:id="@+id/auto_scroll_toggle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_toggle_selector"
                    android:checked="true"
                    android:text=""
                    android:textOff=""
                    android:textOn="" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Auto Scroll"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:layout_marginStart="8dp"
                    android:gravity="center_vertical" />
            </LinearLayout>

            <!-- Monitor Log -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/monitor_log_recycler"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#F5F5F5"
                android:padding="8dp" />

            <TextView
                android:id="@+id/no_logs_text"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:text="No logs available"
                android:textSize="16sp"
                android:textColor="#666666"
                android:gravity="center"
                android:visibility="gone" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>
