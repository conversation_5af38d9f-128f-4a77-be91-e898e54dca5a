<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- System Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <!-- System Icon -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_settings_cell_black_24dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/rounded_corner"
                android:padding="16dp"
                android:tint="#FFFFFF" />

            <!-- System Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Automatic Mobile Recharge System"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#FFFFFF"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- Device ID Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/card_background"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Device ID: "
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#FF0000" />

                    <TextView
                        android:id="@+id/device_id_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textSize="16sp"
                        android:textColor="#FF0000" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <!-- Service Status Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Service Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/service_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Service Status: Checking..."
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <Button
                        android:id="@+id/start_service_button"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="Start Service"
                        android:textColor="#FFFFFF"
                        android:background="@drawable/button_background" />

                    <Button
                        android:id="@+id/stop_service_button"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="Stop Service"
                        android:textColor="#FFFFFF"
                        android:background="@drawable/button_background" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Network Settings Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Network Settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <!-- SIM A -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SIM A:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#333333"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:id="@+id/sim_a_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Item 1"
                        android:textSize="16sp"
                        android:textColor="#666666" />
                </LinearLayout>

                <!-- SIM B -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SIM B:"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#333333"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:id="@+id/sim_b_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Item 1"
                        android:textSize="16sp"
                        android:textColor="#666666" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- System Settings Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="System Settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <!-- SMS Service Toggle -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="SMS Service"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <ToggleButton
                        android:id="@+id/sms_service_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="true"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>
