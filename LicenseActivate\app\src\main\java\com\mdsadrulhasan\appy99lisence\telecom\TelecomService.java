package com.mdsadrulhasan.appy99lisence.telecom;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.mdsadrulhasan.appy99lisence.R;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Main telecom service for handling recharge operations
 * Integrated from backup/app project into LicenseActivate
 */
public class TelecomService extends Service {
    
    private static final String TAG = "TelecomService";
    private static final String CHANNEL_ID = "TelecomServiceChannel";
    private static final int NOTIFICATION_ID = 1001;
    
    // Service components
    private TelecomDbHelper dbHelper;
    private TelecomDialFunction dialFunction;
    private TelecomApiClient apiClient;
    
    // Service state
    private boolean isServiceRunning = false;
    private Handler handler;
    private Runnable serviceRunnable;
    
    // Configuration
    private int checkInterval = 5000; // 5 seconds
    private SharedPreferences serviceSettings;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "TelecomService created");
        
        // Initialize components
        dbHelper = new TelecomDbHelper(getApplicationContext());
        dialFunction = new TelecomDialFunction(getApplicationContext());
        apiClient = new TelecomApiClient(getApplicationContext());
        
        // Initialize handler
        handler = new Handler(Looper.getMainLooper());
        
        // Get service settings
        serviceSettings = getSharedPreferences("telecom_service", Context.MODE_PRIVATE);
        
        // Get check interval from preferences
        String intervalStr = getPreference("check_interval", "5");
        try {
            checkInterval = Integer.parseInt(intervalStr) * 1000;
        } catch (NumberFormatException e) {
            checkInterval = 5000; // Default 5 seconds
        }
        
        // Create notification channel
        createNotificationChannel();
        
        // Setup service runnable
        setupServiceRunnable();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "TelecomService onStartCommand");
        
        // Check if intent is null (service restart)
        if (intent == null) {
            Log.w(TAG, "Service restarted with null intent");
            startForegroundService();
            return START_STICKY;
        }
        
        String action = intent.getStringExtra("action");
        if ("start".equals(action)) {
            startTelecomService();
        } else if ("stop".equals(action)) {
            stopTelecomService();
        } else {
            // Default action - start service
            startTelecomService();
        }
        
        return START_STICKY;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "TelecomService destroyed");
        
        stopTelecomService();
        cleanup();
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // This is a started service, not bound
    }
    
    /**
     * Start telecom service operations
     */
    private void startTelecomService() {
        if (isServiceRunning) {
            Log.d(TAG, "Service already running");
            return;
        }
        
        Log.d(TAG, "Starting telecom service operations");
        isServiceRunning = true;
        
        // Start foreground service
        startForegroundService();
        
        // Start service loop
        handler.post(serviceRunnable);
    }
    
    /**
     * Stop telecom service operations
     */
    private void stopTelecomService() {
        if (!isServiceRunning) {
            Log.d(TAG, "Service already stopped");
            return;
        }
        
        Log.d(TAG, "Stopping telecom service operations");
        isServiceRunning = false;
        
        // Remove pending callbacks
        if (handler != null && serviceRunnable != null) {
            handler.removeCallbacks(serviceRunnable);
        }
        
        // Update service settings
        SharedPreferences.Editor editor = serviceSettings.edit();
        editor.putInt("service_running", 0);
        editor.apply();
    }
    
    /**
     * Setup service runnable for periodic operations
     */
    private void setupServiceRunnable() {
        serviceRunnable = new Runnable() {
            @Override
            public void run() {
                if (isServiceRunning && isServiceEnabled()) {
                    try {
                        // Check and clear busy timeout
                        dialFunction.checkBusyTimeout();
                        
                        // Fetch pending orders from server
                        fetchPendingOrders();
                        
                        // Process local pending recharges
                        processPendingRecharges();
                        
                        // Send pending responses to server
                        sendPendingResponses();
                        
                    } catch (Exception e) {
                        Log.e(TAG, "Error in service loop", e);
                    }
                }
                
                // Schedule next run
                if (isServiceRunning) {
                    handler.postDelayed(this, checkInterval);
                }
            }
        };
    }
    
    /**
     * Check if service is enabled
     */
    private boolean isServiceEnabled() {
        return serviceSettings.getInt("service_enabled", 1) == 1;
    }
    
    /**
     * Fetch pending orders from server
     */
    private void fetchPendingOrders() {
        apiClient.fetchPendingOrders(new TelecomApiClient.ApiCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    apiClient.parseOrdersResponse(response, new TelecomApiClient.OrdersCallback() {
                        @Override
                        public void onOrdersReceived(JSONArray orders) {
                            processServerOrders(orders);
                        }
                        
                        @Override
                        public void onNoOrders() {
                            Log.d(TAG, "No pending orders from server");
                        }
                        
                        @Override
                        public void onError(String error) {
                            Log.e(TAG, "Error processing orders: " + error);
                        }
                    });
                } catch (Exception e) {
                    Log.e(TAG, "Error parsing server response", e);
                }
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "Error fetching orders from server: " + error);
            }
        });
    }
    
    /**
     * Process orders received from server
     */
    private void processServerOrders(JSONArray orders) {
        try {
            for (int i = 0; i < orders.length(); i++) {
                JSONObject order = orders.getJSONObject(i);
                
                String orderId = order.optString("order_id", "");
                String number = order.optString("number", "");
                String amount = order.optString("amount", "");
                String ussd = order.optString("ussd", "");
                String pcode = order.optString("pcode", "");
                int slot = order.optInt("slot", 0);
                int line = order.optInt("line", 0);
                int trigger = order.optInt("trigger", 0);
                int powerload = order.optInt("powerload", 0);
                
                // Insert order into local database
                dbHelper.insertContact(orderId, number, amount, ussd, pcode, line,
                        order.optString("one", ""), order.optString("two", ""),
                        order.optString("three", ""), order.optString("four", ""),
                        order.optString("five", ""), trigger, slot, powerload, 0, 0);
                
                Log.d(TAG, "Processed server order: " + orderId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing server orders", e);
        }
    }
    
    /**
     * Process pending recharges from local database
     */
    private void processPendingRecharges() {
        if (dialFunction.isBusy()) {
            Log.d(TAG, "Dial function is busy, skipping recharge processing");
            return;
        }
        
        try {
            Cursor cursor = dbHelper.getPendingRecharges();
            
            if (cursor != null && cursor.moveToFirst()) {
                String dbId = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_ID));
                String orderId = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_ORDER_ID));
                String ussdCode = cursor.getString(cursor.getColumnIndex(TelecomDbHelper.COLUMN_USSD));
                int slot = cursor.getInt(cursor.getColumnIndex(TelecomDbHelper.COLUMN_SLOT));
                
                Log.d(TAG, "Processing recharge: " + orderId + " with USSD: " + ussdCode);
                
                // Dial USSD code
                boolean success = dialFunction.dialUp(ussdCode, slot, orderId);
                
                if (success) {
                    // Update status to processing
                    dbHelper.updateRechargeStatus(orderId, "processing", null);
                    Log.d(TAG, "USSD dialing initiated for order: " + orderId);
                } else {
                    Log.e(TAG, "Failed to dial USSD for order: " + orderId);
                }
                
                cursor.close();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing pending recharges", e);
        }
    }
    
    /**
     * Send pending responses to server
     */
    private void sendPendingResponses() {
        // Implementation for sending responses to server
        // This would query the database for completed recharges and send responses
        Log.d(TAG, "Checking for pending responses to send to server");
    }
    
    /**
     * Start foreground service with notification
     */
    private void startForegroundService() {
        Notification notification = createServiceNotification();
        startForeground(NOTIFICATION_ID, notification);
    }
    
    /**
     * Create notification for foreground service
     */
    private Notification createServiceNotification() {
        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Telecom Recharge Service")
                .setContentText("Processing mobile recharge requests...")
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .build();
    }
    
    /**
     * Create notification channel for Android O+
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Telecom Service Channel",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Channel for telecom recharge service notifications");
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * Get preference value
     */
    private String getPreference(String key, String defaultValue) {
        return PreferenceManager.getDefaultSharedPreferences(this).getString(key, defaultValue);
    }
    
    /**
     * Cleanup resources
     */
    private void cleanup() {
        if (dbHelper != null) {
            dbHelper.close();
        }
        
        if (dialFunction != null) {
            dialFunction.cleanup();
        }
        
        if (apiClient != null) {
            apiClient.cleanup();
        }
        
        Log.d(TAG, "TelecomService cleanup completed");
    }
    
    /**
     * Static methods for external control
     */
    public static void startTelecomService(Context context) {
        Intent intent = new Intent(context, TelecomService.class);
        intent.putExtra("action", "start");
        context.startService(intent);
    }
    
    public static void stopTelecomService(Context context) {
        Intent intent = new Intent(context, TelecomService.class);
        intent.putExtra("action", "stop");
        context.startService(intent);
    }
}
