# LicenseActivate + Recharge Integration Setup Guide

## ✅ Files Created and Fixed

### 1. Database Setup
**File:** `admin/setup_recharge_tables.sql`
- Complete database schema for recharge functionality
- Run this SQL script on your MySQL database

### 2. Server API
**File:** `admin/api/recharge_api.php`
- Complete recharge API endpoints
- Handles all recharge operations with license validation

### 3. Android Core Components
**Files Created:**
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeDbHelper.java`
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/USSDDialer.java`
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeService.java`
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeApiClient.java`
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeSmsReceiver.java`

### 4. Android Activities
**Files Created:**
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeActivity.java`
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeHistoryActivity.java`
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/recharge/RechargeSettingsActivity.java`

### 5. Layout Files
**Files Created:**
- `LicenseActivate/app/src/main/res/layout/activity_recharge.xml`
- `LicenseActivate/app/src/main/res/layout/activity_recharge_history.xml`
- `LicenseActivate/app/src/main/res/layout/activity_recharge_settings.xml`
- `LicenseActivate/app/src/main/res/layout/item_recharge_history.xml`

### 6. Drawable Resources
**Files Created:**
- `LicenseActivate/app/src/main/res/drawable/spinner_background.xml`

### 7. Configuration Files Updated
**Files Modified:**
- `LicenseActivate/app/src/main/AndroidManifest.xml` - Added permissions and activities
- `LicenseActivate/app/build.gradle` - Added required dependencies
- `LicenseActivate/app/src/main/java/com/mdsadrulhasan/appy99lisence/DashboardActivity.java` - Enhanced with recharge integration
- `LicenseActivate/app/src/main/res/layout/activity_dashboard.xml` - Added recharge section

## 🚀 Deployment Steps

### Step 1: Database Setup
```bash
# Navigate to your MySQL
mysql -u root -p

# Select your database
USE your_database_name;

# Run the setup script
source /path/to/admin/setup_recharge_tables.sql;
```

### Step 2: Server Configuration
1. Upload `admin/api/recharge_api.php` to your server
2. Ensure `admin/config.php` includes database connection
3. Test API endpoint: `http://yourserver.com/admin/api/recharge_api.php?action=get_operators`

### Step 3: Android App Build
1. Open project in Android Studio
2. Sync Gradle files (dependencies will be downloaded)
3. Build the project
4. Install on device for testing

### Step 4: Permissions Setup
The app will request these permissions at runtime:
- Phone calls (for USSD dialing)
- SMS (for response processing)
- Phone state (for dual-SIM detection)

## 🔧 Configuration

### API URL Configuration
Update the API URL in `RechargeApiClient.java`:
```java
private static final String DEFAULT_API_URL = "http://YOUR_SERVER/admin/api/recharge_api.php";
```

### Database Configuration
Ensure your `admin/config.php` has correct database credentials:
```php
$host = "localhost";
$username = "your_db_user";
$password = "your_db_password";
$database = "your_database_name";
```

## 📱 Testing

### 1. License Flow Test
1. Install and open the app
2. Enter license key and PIN
3. Complete domain configuration
4. Verify dashboard shows recharge section

### 2. Recharge Test
1. From dashboard, tap "Recharge" button
2. Enter phone number and amount
3. Select operator and SIM slot
4. Submit recharge
5. Verify USSD code is dialed

### 3. History Test
1. From dashboard, tap "History" button
2. Verify transaction appears in list
3. Check status updates

### 4. Settings Test
1. From dashboard, tap "Settings" button
2. Configure SIM operators
3. Save settings
4. Verify settings are persisted

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. "Resource not found" errors
**Solution:** Ensure all layout and drawable files are created as listed above.

#### 2. "Class not found" errors
**Solution:** Verify all Java files are in correct package structure under `recharge/` folder.

#### 3. Database connection errors
**Solution:** Check `admin/config.php` database credentials and ensure MySQL is running.

#### 4. USSD not dialing
**Solution:** 
- Check phone permissions are granted
- Verify device supports USSD dialing
- Test with different SIM slots

#### 5. SMS not being processed
**Solution:**
- Grant SMS permissions
- Check SMS receiver is registered in manifest
- Verify SMS contains recharge keywords

### Debug Logs
Enable debug logging by checking Android Studio Logcat for:
- `RechargeActivity`
- `RechargeService`
- `USSDDialer`
- `RechargeSmsReceiver`

## 📋 Verification Checklist

### ✅ Database
- [ ] Tables created successfully
- [ ] Default operators inserted
- [ ] Foreign key relationships working

### ✅ Server API
- [ ] API endpoints responding
- [ ] License validation working
- [ ] Error handling functional

### ✅ Android App
- [ ] App builds without errors
- [ ] All activities launch correctly
- [ ] Permissions requested properly
- [ ] USSD dialing works
- [ ] SMS processing functional

### ✅ Integration
- [ ] License → Recharge flow works
- [ ] Data synchronization working
- [ ] Settings persistence functional
- [ ] Transaction history displays

## 🎯 Success Criteria

1. **Seamless Integration**: Users can access recharge after license activation
2. **Functional USSD**: App successfully dials USSD codes
3. **SMS Processing**: Automatic processing of operator responses
4. **Data Integrity**: All transactions properly logged and synced
5. **Error Handling**: Graceful handling of failures and edge cases

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Android Studio Logcat for error messages
3. Verify all files are created and in correct locations
4. Test database connectivity separately
5. Ensure all permissions are granted on device

The integration is now complete and ready for testing! 🎉
