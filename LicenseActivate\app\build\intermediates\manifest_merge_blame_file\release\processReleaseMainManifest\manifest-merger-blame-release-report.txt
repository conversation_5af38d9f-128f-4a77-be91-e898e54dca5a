1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdsadrulhasan.appy99lisence"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Network state permission to check connectivity -->
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- Notification permissions for real-time notifications and expiration warnings -->
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:13:22-63
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:14:5-68
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:14:22-65
21
22    <!-- Telecom recharge permissions -->
23    <uses-permission android:name="android.permission.CALL_PHONE" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:5-69
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:22-66
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:5-75
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:22-72
25    <uses-permission android:name="android.permission.RECEIVE_SMS" />
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:5-70
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:22-67
26    <uses-permission android:name="android.permission.READ_SMS" />
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:5-67
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:22-64
27    <uses-permission android:name="android.permission.SEND_SMS" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:5-67
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:22-64
28    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22:5-85
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:22:22-82
29    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:5-78
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:22-75
30    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:5-77
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:22-74
31    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:5-95
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:22-92
32
33    <!-- Additional permissions for enhanced notification functionality -->
34    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
34-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:28:5-81
34-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:28:22-78
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:5-77
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:24:22-74
36
37    <!-- Recharge module permissions -->
38    <uses-permission android:name="android.permission.CALL_PHONE" />
38-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:5-69
38-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:17:22-66
39    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
39-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:5-75
39-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:18:22-72
40    <uses-permission android:name="android.permission.RECEIVE_SMS" />
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:5-70
40-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:19:22-67
41    <uses-permission android:name="android.permission.READ_SMS" />
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:5-67
41-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:20:22-64
42    <uses-permission android:name="android.permission.SEND_SMS" />
42-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:5-67
42-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:21:22-64
43    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
43-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:5-78
43-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:23:22-75
44    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
44-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:5-95
44-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:25:22-92
45
46    <!-- Telecom permissions for dual-SIM support -->
47    <uses-permission android:name="android.permission.CALL_PRIVILEGED" />
47-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41:5-74
47-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:41:22-71
48    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
48-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42:5-77
48-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:42:22-74
49
50    <!-- Background service permissions -->
51    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
51-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:45:5-88
51-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:45:22-85
52    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:46:5-87
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:46:22-84
53
54    <permission
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.mdsadrulhasan.appy99lisence.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
59
60    <application
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:48:5-156:19
61        android:allowBackup="true"
61-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:49:9-35
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45cfe67c7755d3e66160b5bcd91d999e\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
63        android:dataExtractionRules="@xml/data_extraction_rules"
63-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:50:9-65
64        android:extractNativeLibs="false"
65        android:fullBackupContent="@xml/backup_rules"
65-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:51:9-54
66        android:icon="@mipmap/ic_launcher"
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:52:9-43
67        android:label="@string/app_name"
67-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:53:9-41
68        android:networkSecurityConfig="@xml/network_security_config"
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:57:9-69
69        android:roundIcon="@mipmap/ic_launcher_round"
69-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:54:9-54
70        android:supportsRtl="true"
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:55:9-35
71        android:theme="@style/Theme.Appy99Lisence"
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:56:9-51
72        android:usesCleartextTraffic="true" >
72-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:58:9-44
73        <activity
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:60:9-68:20
74            android:name="com.mdsadrulhasan.appy99lisence.MainActivity"
74-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:61:13-41
75            android:exported="true" >
75-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:62:13-36
76            <intent-filter>
76-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:63:13-67:29
77                <action android:name="android.intent.action.MAIN" />
77-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:17-69
77-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:64:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:66:17-77
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:66:27-74
80            </intent-filter>
81        </activity>
82        <activity
82-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:70:9-77:20
83            android:name="com.mdsadrulhasan.appy99lisence.DashboardActivity"
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:71:13-46
84            android:exported="false"
84-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:72:13-37
85            android:parentActivityName="com.mdsadrulhasan.appy99lisence.MainActivity" >
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:73:13-55
86            <meta-data
86-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
87                android:name="android.support.PARENT_ACTIVITY"
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
88                android:value=".MainActivity" />
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
89        </activity>
90
91        <!-- Recharge Module Activities -->
92        <activity
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:80:9-87:20
93            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity"
93-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:81:13-54
94            android:exported="false"
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:82:13-37
95            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:83:13-60
96            <meta-data
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
97                android:name="android.support.PARENT_ACTIVITY"
97-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
98                android:value=".DashboardActivity" />
98-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
99        </activity>
100        <activity
100-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:89:9-96:20
101            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity"
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:90:13-61
102            android:exported="false"
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:91:13-37
103            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
103-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:92:13-60
104            <meta-data
104-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
105                android:name="android.support.PARENT_ACTIVITY"
105-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
106                android:value=".DashboardActivity" />
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
107        </activity>
108        <activity
108-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:98:9-105:20
109            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity"
109-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:99:13-62
110            android:exported="false"
110-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:100:13-37
111            android:parentActivityName="com.mdsadrulhasan.appy99lisence.DashboardActivity" >
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:101:13-60
112            <meta-data
112-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:74:13-76:49
113                android:name="android.support.PARENT_ACTIVITY"
113-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:75:17-63
114                android:value=".DashboardActivity" />
114-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:76:17-46
115        </activity>
116
117        <!-- Recharge Background Service -->
118        <service
118-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:110:9-114:66
119            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeService"
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:111:13-53
120            android:enabled="true"
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:112:13-35
121            android:exported="false"
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:113:13-37
122            android:foregroundServiceType="phoneCall|dataSync" />
122-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:114:13-63
123
124        <!-- SMS Receiver for recharge responses -->
125        <receiver
125-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:117:9-124:20
126            android:name="com.mdsadrulhasan.appy99lisence.recharge.RechargeSmsReceiver"
126-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:118:13-57
127            android:enabled="true"
127-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:119:13-35
128            android:exported="true" >
128-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:120:13-36
129            <intent-filter android:priority="1000" >
129-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:13-123:29
129-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:28-51
130                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
130-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:17-82
130-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:25-79
131            </intent-filter>
132        </receiver>
133
134        <!-- Telecom Services -->
135        <service
135-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:127:9-131:66
136            android:name="com.mdsadrulhasan.appy99lisence.telecom.TelecomService"
136-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:128:13-51
137            android:enabled="true"
137-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:129:13-35
138            android:exported="false"
138-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:130:13-37
139            android:foregroundServiceType="phoneCall|dataSync" />
139-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:131:13-63
140
141        <!-- Telecom SMS Receiver -->
142        <receiver
142-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:134:9-141:20
143            android:name="com.mdsadrulhasan.appy99lisence.telecom.TelecomSmsReceiver"
143-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:135:13-55
144            android:enabled="true"
144-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:136:13-35
145            android:exported="true" >
145-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:137:13-36
146            <intent-filter android:priority="1000" >
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:13-123:29
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:121:28-51
147                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:17-82
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:122:25-79
148            </intent-filter>
149        </receiver>
150
151        <!-- Telecom USSD Accessibility Service -->
152        <service
152-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:144:9-154:19
153            android:name="com.mdsadrulhasan.appy99lisence.telecom.TelecomUSSDService"
153-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:145:13-55
154            android:exported="false"
154-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:147:13-37
155            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
155-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:146:13-79
156            <intent-filter>
156-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:148:13-150:29
157                <action android:name="android.accessibilityservice.AccessibilityService" />
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:149:17-92
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:149:25-89
158            </intent-filter>
159
160            <meta-data
160-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:151:13-153:80
161                android:name="android.accessibilityservice"
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:152:17-60
162                android:resource="@xml/telecom_accessibility_service_config" />
162-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\app\src\main\AndroidManifest.xml:153:17-77
163        </service>
164
165        <provider
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
166            android:name="androidx.startup.InitializationProvider"
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
167            android:authorities="com.mdsadrulhasan.appy99lisence.androidx-startup"
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
168            android:exported="false" >
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
169            <meta-data
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.emoji2.text.EmojiCompatInitializer"
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
171                android:value="androidx.startup" />
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\684bad9d71c4492008bcb6785843b462\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
173-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
174                android:value="androidx.startup" />
174-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\765eeba732fa755dc51fe4858b7df9fd\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
175            <meta-data
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
176                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
177                android:value="androidx.startup" />
177-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
178        </provider>
179
180        <uses-library
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
181            android:name="androidx.window.extensions"
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
182            android:required="false" />
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
183        <uses-library
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
184            android:name="androidx.window.sidecar"
184-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
185            android:required="false" />
185-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9756fefba6eac8a4a85fb62b959a2678\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
186
187        <receiver
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
188            android:name="androidx.profileinstaller.ProfileInstallReceiver"
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
189            android:directBootAware="false"
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
190            android:enabled="true"
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
191            android:exported="true"
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
192            android:permission="android.permission.DUMP" >
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
194                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
197                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
200                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
203                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d6066a33739392bbd96dded965ef2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
204            </intent-filter>
205        </receiver>
206    </application>
207
208</manifest>
