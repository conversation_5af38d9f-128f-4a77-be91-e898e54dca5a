<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#F8F9FA" />
            <corners android:radius="12dp" />
            <stroke android:width="2dp" android:color="#2196F3" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F8F9FA" />
            <corners android:radius="12dp" />
            <stroke android:width="1dp" android:color="#E0E0E0" />
        </shape>
    </item>
    
</selector>
