#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1076816 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=10236, tid=19612
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 29 04:42:38 2025 Bangladesh Standard Time elapsed time: 24.126311 seconds (0d 0h 0m 24s)

---------------  T H R E A D  ---------------

Current thread (0x000001916fd43fe0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=19612, stack(0x0000006f27f00000,0x0000006f28000000) (1024K)]


Current CompileTask:
C2:  24126 17696   !   4       com.android.tools.r8.dex.C::k (2746 bytes)

Stack: [0x0000006f27f00000,0x0000006f28000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0xc3f3d]
V  [jvm.dll+0xc4473]
V  [jvm.dll+0x2f437f]
V  [jvm.dll+0x5f5056]
V  [jvm.dll+0x252d7f]
V  [jvm.dll+0x25315f]
V  [jvm.dll+0x24b8e0]
V  [jvm.dll+0x2490a3]
V  [jvm.dll+0x1c9634]
V  [jvm.dll+0x258859]
V  [jvm.dll+0x256e3a]
V  [jvm.dll+0x3ef6c6]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000191784e2ea0, length=151, elements={
0x00000191532a76f0, 0x000001916fca4d90, 0x000001916fca5ab0, 0x000001916fca6a50,
0x000001916fca84c0, 0x000001916fca8f20, 0x000001916fca9980, 0x000001916fd43fe0,
0x000001916fd64a90, 0x000001916fe3dbb0, 0x000001916ff6d220, 0x000001917606ec10,
0x0000019175fef8f0, 0x00000191761d1800, 0x00000191763099f0, 0x00000191763065e0,
0x000001917632b480, 0x00000191760c9ac0, 0x00000191760cae70, 0x00000191760ca150,
0x00000191760c8da0, 0x00000191760c9430, 0x00000191760c8710, 0x00000191760ca7e0,
0x00000191760cbb90, 0x0000019176bc5290, 0x0000019176bc3ee0, 0x0000019176bc31c0,
0x0000019176bc24a0, 0x0000019176bc5920, 0x0000019176bc2b30, 0x0000019176bc3850,
0x0000019176bc4c00, 0x0000019177da7bd0, 0x0000019177da6820, 0x0000019177da5b00,
0x0000019177dab050, 0x0000019177daa9c0, 0x0000019177da8f80, 0x0000019177dad120,
0x0000019177dab6e0, 0x0000019177da6eb0, 0x0000019177da88f0, 0x0000019177dabd70,
0x0000019177da6190, 0x0000019177da7540, 0x0000019177da8260, 0x0000019177da9610,
0x0000019177da9ca0, 0x000001917b23f670, 0x000001917b2410b0, 0x000001917b240a20,
0x000001917b241740, 0x000001917b240390, 0x000001917b241dd0, 0x000001917b242460,
0x000001917b23d5a0, 0x000001917b23b4d0, 0x000001917b23dc30, 0x000001917b242af0,
0x000001916ff90560, 0x000001916ff8eb20, 0x000001916ff92cc0, 0x000001916ff8f1b0,
0x000001916ff8fed0, 0x000001916ff90bf0, 0x000001916ff8d0e0, 0x000001916ff91280,
0x000001916ff8d770, 0x000001916ff8de00, 0x000001916ff91fa0, 0x000001916ff8c3c0,
0x000001916ff939e0, 0x000001916ff8ca50, 0x000001916ff8e490, 0x000001917b23c880,
0x000001917b23fd00, 0x0000019177daca90, 0x0000019179cc34c0, 0x0000019179cc8a10,
0x0000019179a13200, 0x0000019179a0f060, 0x0000019179a15ff0, 0x0000019179a15960,
0x0000019179a16680, 0x0000019179a0f6f0, 0x0000019179a13f20, 0x0000019176e80b20,
0x0000019179cc1a80, 0x0000019179cc5c20, 0x0000019179cc4f00, 0x0000019179cc3b50,
0x0000019179cc62b0, 0x0000019179cc7cf0, 0x0000019177a035e0, 0x0000019177a007f0,
0x0000019177a05d40, 0x0000019177a00e80, 0x00000191779ffad0, 0x0000019177a056b0,
0x0000019177a04990, 0x0000019177a028c0, 0x0000019177a05020, 0x0000019177a063d0,
0x0000019177a04300, 0x0000019177a01510, 0x0000019177a00160, 0x0000019177a01ba0,
0x0000019177a06a60, 0x0000019177a02230, 0x0000019177a070f0, 0x0000019179a11130,
0x0000019179a14c40, 0x0000019179a117c0, 0x0000019179a124e0, 0x0000019179a12b70,
0x0000019179a13890, 0x0000019179a145b0, 0x0000019179a152d0, 0x0000019179c5cf70,
0x0000019179c5dc90, 0x0000019179c5f6d0, 0x0000019179c5c250, 0x0000019179c5e320,
0x0000019179c5e9b0, 0x0000019179c5f040, 0x0000019179c5c8e0, 0x0000019179cc5590,
0x0000019179cc27a0, 0x0000019179cc41e0, 0x000001917c98a3a0, 0x000001917c98cb00,
0x000001917c98bde0, 0x000001917c98f260, 0x000001917c98b0c0, 0x000001917c98aa30,
0x000001917c98f8f0, 0x000001917c98ff80, 0x000001917c98c470, 0x000001917c989680,
0x000001917d4fe0f0, 0x000001917d4fee10, 0x000001917d4f9230, 0x000001917c98d820,
0x000001917c98e540, 0x000001917c990610, 0x0000019179cc2110, 0x0000019179cc2e30,
0x0000019179c5d600, 0x000001917e0131c0, 0x000001917e011780
}

Java Threads: ( => current thread )
  0x00000191532a76f0 JavaThread "main"                              [_thread_blocked, id=11116, stack(0x0000006f27100000,0x0000006f27200000) (1024K)]
  0x000001916fca4d90 JavaThread "Reference Handler"          daemon [_thread_blocked, id=21756, stack(0x0000006f27900000,0x0000006f27a00000) (1024K)]
  0x000001916fca5ab0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=8740, stack(0x0000006f27a00000,0x0000006f27b00000) (1024K)]
  0x000001916fca6a50 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=20104, stack(0x0000006f27b00000,0x0000006f27c00000) (1024K)]
  0x000001916fca84c0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19780, stack(0x0000006f27c00000,0x0000006f27d00000) (1024K)]
  0x000001916fca8f20 JavaThread "Service Thread"             daemon [_thread_blocked, id=22804, stack(0x0000006f27d00000,0x0000006f27e00000) (1024K)]
  0x000001916fca9980 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=18124, stack(0x0000006f27e00000,0x0000006f27f00000) (1024K)]
=>0x000001916fd43fe0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=19612, stack(0x0000006f27f00000,0x0000006f28000000) (1024K)]
  0x000001916fd64a90 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21892, stack(0x0000006f28000000,0x0000006f28100000) (1024K)]
  0x000001916fe3dbb0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=5276, stack(0x0000006f28100000,0x0000006f28200000) (1024K)]
  0x000001916ff6d220 JavaThread "Notification Thread"        daemon [_thread_blocked, id=21896, stack(0x0000006f28200000,0x0000006f28300000) (1024K)]
  0x000001917606ec10 JavaThread "Daemon health stats"               [_thread_blocked, id=8884, stack(0x0000006f28400000,0x0000006f28500000) (1024K)]
  0x0000019175fef8f0 JavaThread "Incoming local TCP Connector on port 63775"        [_thread_in_native, id=6904, stack(0x0000006f28c00000,0x0000006f28d00000) (1024K)]
  0x00000191761d1800 JavaThread "Daemon periodic checks"            [_thread_blocked, id=5036, stack(0x0000006f28d00000,0x0000006f28e00000) (1024K)]
  0x00000191763099f0 JavaThread "Daemon"                            [_thread_blocked, id=18840, stack(0x0000006f28e00000,0x0000006f28f00000) (1024K)]
  0x00000191763065e0 JavaThread "Handler for socket connection from /127.0.0.1:63775 to /127.0.0.1:63776"        [_thread_in_native, id=22428, stack(0x0000006f28f00000,0x0000006f29000000) (1024K)]
  0x000001917632b480 JavaThread "Cancel handler"                    [_thread_blocked, id=4996, stack(0x0000006f29000000,0x0000006f29100000) (1024K)]
  0x00000191760c9ac0 JavaThread "Daemon worker"                     [_thread_blocked, id=12480, stack(0x0000006f29100000,0x0000006f29200000) (1024K)]
  0x00000191760cae70 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:63775 to /127.0.0.1:63776"        [_thread_blocked, id=9948, stack(0x0000006f29200000,0x0000006f29300000) (1024K)]
  0x00000191760ca150 JavaThread "Stdin handler"                     [_thread_blocked, id=16636, stack(0x0000006f29300000,0x0000006f29400000) (1024K)]
  0x00000191760c8da0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=8596, stack(0x0000006f29400000,0x0000006f29500000) (1024K)]
  0x00000191760c9430 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=2140, stack(0x0000006f29500000,0x0000006f29600000) (1024K)]
  0x00000191760c8710 JavaThread "File lock request listener"        [_thread_in_native, id=1792, stack(0x0000006f29600000,0x0000006f29700000) (1024K)]
  0x00000191760ca7e0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)"        [_thread_blocked, id=5596, stack(0x0000006f29700000,0x0000006f29800000) (1024K)]
  0x00000191760cbb90 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)"        [_thread_blocked, id=1256, stack(0x0000006f29900000,0x0000006f29a00000) (1024K)]
  0x0000019176bc5290 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\.gradle\8.11.1\fileHashes)"        [_thread_blocked, id=15704, stack(0x0000006f29b00000,0x0000006f29c00000) (1024K)]
  0x0000019176bc3ee0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\.gradle\buildOutputCleanup)"        [_thread_blocked, id=22184, stack(0x0000006f29c00000,0x0000006f29d00000) (1024K)]
  0x0000019176bc31c0 JavaThread "File watcher server"        daemon [_thread_in_native, id=15060, stack(0x0000006f29d00000,0x0000006f29e00000) (1024K)]
  0x0000019176bc24a0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=19916, stack(0x0000006f29e00000,0x0000006f29f00000) (1024K)]
  0x0000019176bc5920 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\.gradle\8.11.1\checksums)"        [_thread_blocked, id=11020, stack(0x0000006f29800000,0x0000006f29900000) (1024K)]
  0x0000019176bc2b30 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.11.1\md-rule)"        [_thread_blocked, id=23012, stack(0x0000006f29f00000,0x0000006f2a000000) (1024K)]
  0x0000019176bc3850 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.11.1\md-supplier)"        [_thread_blocked, id=15868, stack(0x0000006f2a000000,0x0000006f2a100000) (1024K)]
  0x0000019176bc4c00 JavaThread "jar transforms"                    [_thread_blocked, id=19028, stack(0x0000006f2a200000,0x0000006f2a300000) (1024K)]
  0x0000019177da7bd0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=2572, stack(0x0000006f2a300000,0x0000006f2a400000) (1024K)]
  0x0000019177da6820 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=21968, stack(0x0000006f2a400000,0x0000006f2a500000) (1024K)]
  0x0000019177da5b00 JavaThread "Unconstrained build operations"        [_thread_blocked, id=2592, stack(0x0000006f2a500000,0x0000006f2a600000) (1024K)]
  0x0000019177dab050 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=18580, stack(0x0000006f2a600000,0x0000006f2a700000) (1024K)]
  0x0000019177daa9c0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=15624, stack(0x0000006f2a700000,0x0000006f2a800000) (1024K)]
  0x0000019177da8f80 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=12420, stack(0x0000006f2a800000,0x0000006f2a900000) (1024K)]
  0x0000019177dad120 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=23172, stack(0x0000006f2a900000,0x0000006f2aa00000) (1024K)]
  0x0000019177dab6e0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=2192, stack(0x0000006f2aa00000,0x0000006f2ab00000) (1024K)]
  0x0000019177da6eb0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=4724, stack(0x0000006f2ab00000,0x0000006f2ac00000) (1024K)]
  0x0000019177da88f0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=9656, stack(0x0000006f2ac00000,0x0000006f2ad00000) (1024K)]
  0x0000019177dabd70 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=13128, stack(0x0000006f2ad00000,0x0000006f2ae00000) (1024K)]
  0x0000019177da6190 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=15984, stack(0x0000006f2ae00000,0x0000006f2af00000) (1024K)]
  0x0000019177da7540 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=15052, stack(0x0000006f2af00000,0x0000006f2b000000) (1024K)]
  0x0000019177da8260 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=21380, stack(0x0000006f2b000000,0x0000006f2b100000) (1024K)]
  0x0000019177da9610 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=13656, stack(0x0000006f2b100000,0x0000006f2b200000) (1024K)]
  0x0000019177da9ca0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=17404, stack(0x0000006f2b200000,0x0000006f2b300000) (1024K)]
  0x000001917b23f670 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=19800, stack(0x0000006f2b300000,0x0000006f2b400000) (1024K)]
  0x000001917b2410b0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=20660, stack(0x0000006f2b400000,0x0000006f2b500000) (1024K)]
  0x000001917b240a20 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=21576, stack(0x0000006f2b500000,0x0000006f2b600000) (1024K)]
  0x000001917b241740 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=20960, stack(0x0000006f2b600000,0x0000006f2b700000) (1024K)]
  0x000001917b240390 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=21724, stack(0x0000006f2b700000,0x0000006f2b800000) (1024K)]
  0x000001917b241dd0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=6980, stack(0x0000006f2b800000,0x0000006f2b900000) (1024K)]
  0x000001917b242460 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=16464, stack(0x0000006f2b900000,0x0000006f2ba00000) (1024K)]
  0x000001917b23d5a0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=13688, stack(0x0000006f2ba00000,0x0000006f2bb00000) (1024K)]
  0x000001917b23b4d0 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=22084, stack(0x0000006f2bb00000,0x0000006f2bc00000) (1024K)]
  0x000001917b23dc30 JavaThread "Memory manager"                    [_thread_blocked, id=22956, stack(0x0000006f2bc00000,0x0000006f2bd00000) (1024K)]
  0x000001917b242af0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=9524, stack(0x0000006f2bd00000,0x0000006f2be00000) (1024K)]
  0x000001916ff90560 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=2636, stack(0x0000006f2be00000,0x0000006f2bf00000) (1024K)]
  0x000001916ff8eb20 JavaThread "build event listener"              [_thread_blocked, id=21980, stack(0x0000006f2bf00000,0x0000006f2c000000) (1024K)]
  0x000001916ff92cc0 JavaThread "included builds"                   [_thread_blocked, id=2224, stack(0x0000006f2c000000,0x0000006f2c100000) (1024K)]
  0x000001916ff8f1b0 JavaThread "Execution worker"                  [_thread_blocked, id=8964, stack(0x0000006f2c100000,0x0000006f2c200000) (1024K)]
  0x000001916ff8fed0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=6124, stack(0x0000006f2c200000,0x0000006f2c300000) (1024K)]
  0x000001916ff90bf0 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=18064, stack(0x0000006f2c300000,0x0000006f2c400000) (1024K)]
  0x000001916ff8d0e0 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=1896, stack(0x0000006f2c400000,0x0000006f2c500000) (1024K)]
  0x000001916ff91280 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=8512, stack(0x0000006f2c500000,0x0000006f2c600000) (1024K)]
  0x000001916ff8d770 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=5460, stack(0x0000006f2c600000,0x0000006f2c700000) (1024K)]
  0x000001916ff8de00 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=13616, stack(0x0000006f2c700000,0x0000006f2c800000) (1024K)]
  0x000001916ff91fa0 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivate\.gradle\8.11.1\executionHistory)"        [_thread_blocked, id=21280, stack(0x0000006f2c800000,0x0000006f2c900000) (1024K)]
  0x000001916ff8c3c0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=14136, stack(0x0000006f28300000,0x0000006f28400000) (1024K)]
  0x000001916ff939e0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=2992, stack(0x0000006f2c900000,0x0000006f2ca00000) (1024K)]
  0x000001916ff8ca50 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=17992, stack(0x0000006f2ca00000,0x0000006f2cb00000) (1024K)]
  0x000001916ff8e490 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=18116, stack(0x0000006f2cb00000,0x0000006f2cc00000) (1024K)]
  0x000001917b23c880 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=12916, stack(0x0000006f2cc00000,0x0000006f2cd00000) (1024K)]
  0x000001917b23fd00 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=14712, stack(0x0000006f2cd00000,0x0000006f2ce00000) (1024K)]
  0x0000019177daca90 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=10876, stack(0x0000006f2ce00000,0x0000006f2cf00000) (1024K)]
  0x0000019179cc34c0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=12688, stack(0x0000006f2cf00000,0x0000006f2d000000) (1024K)]
  0x0000019179cc8a10 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=23140, stack(0x0000006f2d000000,0x0000006f2d100000) (1024K)]
  0x0000019179a13200 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=23528, stack(0x0000006f2d200000,0x0000006f2d300000) (1024K)]
  0x0000019179a0f060 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=18704, stack(0x0000006f2d300000,0x0000006f2d400000) (1024K)]
  0x0000019179a15ff0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=21656, stack(0x0000006f2d400000,0x0000006f2d500000) (1024K)]
  0x0000019179a15960 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=17500, stack(0x0000006f2d500000,0x0000006f2d600000) (1024K)]
  0x0000019179a16680 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=15576, stack(0x0000006f2d600000,0x0000006f2d700000) (1024K)]
  0x0000019179a0f6f0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=3156, stack(0x0000006f2d700000,0x0000006f2d800000) (1024K)]
  0x0000019179a13f20 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=1560, stack(0x0000006f2d800000,0x0000006f2d900000) (1024K)]
  0x0000019176e80b20 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=6704, stack(0x0000006f2a100000,0x0000006f2a200000) (1024K)]
  0x0000019179cc1a80 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=15764, stack(0x0000006f2d900000,0x0000006f2da00000) (1024K)]
  0x0000019179cc5c20 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=22044, stack(0x0000006f2da00000,0x0000006f2db00000) (1024K)]
  0x0000019179cc4f00 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=18092, stack(0x0000006f2db00000,0x0000006f2dc00000) (1024K)]
  0x0000019179cc3b50 JavaThread "WorkerExecutor Queue Thread 6"        [_thread_blocked, id=11552, stack(0x0000006f2dc00000,0x0000006f2dd00000) (1024K)]
  0x0000019179cc62b0 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=22824, stack(0x0000006f2dd00000,0x0000006f2de00000) (1024K)]
  0x0000019179cc7cf0 JavaThread "WorkerExecutor Queue Thread 8"        [_thread_blocked, id=9744, stack(0x0000006f2de00000,0x0000006f2df00000) (1024K)]
  0x0000019177a035e0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=23224, stack(0x0000006f2df00000,0x0000006f2e000000) (1024K)]
  0x0000019177a007f0 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=23436, stack(0x0000006f2e000000,0x0000006f2e100000) (1024K)]
  0x0000019177a05d40 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=4472, stack(0x0000006f2e100000,0x0000006f2e200000) (1024K)]
  0x0000019177a00e80 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=13824, stack(0x0000006f2e200000,0x0000006f2e300000) (1024K)]
  0x00000191779ffad0 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=14112, stack(0x0000006f2e300000,0x0000006f2e400000) (1024K)]
  0x0000019177a056b0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=12696, stack(0x0000006f2e400000,0x0000006f2e500000) (1024K)]
  0x0000019177a04990 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=1512, stack(0x0000006f2e500000,0x0000006f2e600000) (1024K)]
  0x0000019177a028c0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=9532, stack(0x0000006f2e600000,0x0000006f2e700000) (1024K)]
  0x0000019177a05020 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=15068, stack(0x0000006f2e700000,0x0000006f2e800000) (1024K)]
  0x0000019177a063d0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=15180, stack(0x0000006f2e800000,0x0000006f2e900000) (1024K)]
  0x0000019177a04300 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=16316, stack(0x0000006f2e900000,0x0000006f2ea00000) (1024K)]
  0x0000019177a01510 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=13840, stack(0x0000006f2ea00000,0x0000006f2eb00000) (1024K)]
  0x0000019177a00160 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=8888, stack(0x0000006f2eb00000,0x0000006f2ec00000) (1024K)]
  0x0000019177a01ba0 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=8720, stack(0x0000006f2ec00000,0x0000006f2ed00000) (1024K)]
  0x0000019177a06a60 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=3012, stack(0x0000006f2ed00000,0x0000006f2ee00000) (1024K)]
  0x0000019177a02230 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=3428, stack(0x0000006f2ee00000,0x0000006f2ef00000) (1024K)]
  0x0000019177a070f0 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=21404, stack(0x0000006f2ef00000,0x0000006f2f000000) (1024K)]
  0x0000019179a11130 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=13920, stack(0x0000006f2f000000,0x0000006f2f100000) (1024K)]
  0x0000019179a14c40 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=10116, stack(0x0000006f2f100000,0x0000006f2f200000) (1024K)]
  0x0000019179a117c0 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=10444, stack(0x0000006f2f200000,0x0000006f2f300000) (1024K)]
  0x0000019179a124e0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=10544, stack(0x0000006f2f300000,0x0000006f2f400000) (1024K)]
  0x0000019179a12b70 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=13556, stack(0x0000006f2f400000,0x0000006f2f500000) (1024K)]
  0x0000019179a13890 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=9592, stack(0x0000006f2f500000,0x0000006f2f600000) (1024K)]
  0x0000019179a145b0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=9632, stack(0x0000006f2f600000,0x0000006f2f700000) (1024K)]
  0x0000019179a152d0 JavaThread "WorkerExecutor Queue Thread 9"        [_thread_blocked, id=11428, stack(0x0000006f2f700000,0x0000006f2f800000) (1024K)]
  0x0000019179c5cf70 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=12580, stack(0x0000006f2f800000,0x0000006f2f900000) (1024K)]
  0x0000019179c5dc90 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=19812, stack(0x0000006f2f900000,0x0000006f2fa00000) (1024K)]
  0x0000019179c5f6d0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=15416, stack(0x0000006f2fa00000,0x0000006f2fb00000) (1024K)]
  0x0000019179c5c250 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=15276, stack(0x0000006f2fb00000,0x0000006f2fc00000) (1024K)]
  0x0000019179c5e320 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=2396, stack(0x0000006f2fc00000,0x0000006f2fd00000) (1024K)]
  0x0000019179c5e9b0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=18364, stack(0x0000006f2fd00000,0x0000006f2fe00000) (1024K)]
  0x0000019179c5f040 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=16264, stack(0x0000006f2fe00000,0x0000006f2ff00000) (1024K)]
  0x0000019179c5c8e0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=1652, stack(0x0000006f2ff00000,0x0000006f30000000) (1024K)]
  0x0000019179cc5590 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=18480, stack(0x0000006f30000000,0x0000006f30100000) (1024K)]
  0x0000019179cc27a0 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=12832, stack(0x0000006f30100000,0x0000006f30200000) (1024K)]
  0x0000019179cc41e0 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=17936, stack(0x0000006f30200000,0x0000006f30300000) (1024K)]
  0x000001917c98a3a0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=21904, stack(0x0000006f30300000,0x0000006f30400000) (1024K)]
  0x000001917c98cb00 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=16888, stack(0x0000006f30400000,0x0000006f30500000) (1024K)]
  0x000001917c98bde0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=11168, stack(0x0000006f30500000,0x0000006f30600000) (1024K)]
  0x000001917c98f260 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=21592, stack(0x0000006f30600000,0x0000006f30700000) (1024K)]
  0x000001917c98b0c0 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=3196, stack(0x0000006f30700000,0x0000006f30800000) (1024K)]
  0x000001917c98aa30 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=7800, stack(0x0000006f30800000,0x0000006f30900000) (1024K)]
  0x000001917c98f8f0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=9184, stack(0x0000006f30900000,0x0000006f30a00000) (1024K)]
  0x000001917c98ff80 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=5700, stack(0x0000006f30a00000,0x0000006f30b00000) (1024K)]
  0x000001917c98c470 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=14588, stack(0x0000006f30b00000,0x0000006f30c00000) (1024K)]
  0x000001917c989680 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=20584, stack(0x0000006f30c00000,0x0000006f30d00000) (1024K)]
  0x000001917d4fe0f0 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=21744, stack(0x0000006f30d00000,0x0000006f30e00000) (1024K)]
  0x000001917d4fee10 JavaThread "stderr"                            [_thread_in_native, id=12912, stack(0x0000006f30e00000,0x0000006f30f00000) (1024K)]
  0x000001917d4f9230 JavaThread "stdout"                            [_thread_in_native, id=12012, stack(0x0000006f30f00000,0x0000006f31000000) (1024K)]
  0x000001917c98d820 JavaThread "ForkJoinPool-1-worker-1"    daemon [_thread_blocked, id=16104, stack(0x0000006f2d100000,0x0000006f2d200000) (1024K)]
  0x000001917c98e540 JavaThread "ForkJoinPool-1-worker-2"    daemon [_thread_in_vm, id=14844, stack(0x0000006f31000000,0x0000006f31100000) (1024K)]
  0x000001917c990610 JavaThread "ForkJoinPool-1-worker-3"    daemon [_thread_blocked, id=18624, stack(0x0000006f31100000,0x0000006f31200000) (1024K)]
  0x0000019179cc2110 JavaThread "ForkJoinPool-1-worker-4"    daemon [_thread_blocked, id=18948, stack(0x0000006f31200000,0x0000006f31300000) (1024K)]
  0x0000019179cc2e30 JavaThread "ForkJoinPool-1-worker-5"    daemon [_thread_blocked, id=22228, stack(0x0000006f31300000,0x0000006f31400000) (1024K)]
  0x0000019179c5d600 JavaThread "ForkJoinPool-1-worker-6"    daemon [_thread_blocked, id=23260, stack(0x0000006f31400000,0x0000006f31500000) (1024K)]
  0x000001917e0131c0 JavaThread "ForkJoinPool-1-worker-7"    daemon [_thread_blocked, id=16800, stack(0x0000006f31500000,0x0000006f31600000) (1024K)]
  0x000001917e011780 JavaThread "ForkJoinPool-1-worker-8"    daemon [_thread_blocked, id=19684, stack(0x0000006f31600000,0x0000006f31700000) (1024K)]
Total: 151

Other Threads:
  0x000001916fc859c0 VMThread "VM Thread"                           [id=21252, stack(0x0000006f27800000,0x0000006f27900000) (1024K)]
  0x000001916fc73530 WatcherThread "VM Periodic Task Thread"        [id=12296, stack(0x0000006f27700000,0x0000006f27800000) (1024K)]
  0x00000191532ffe70 WorkerThread "GC Thread#0"                     [id=4496, stack(0x0000006f27200000,0x0000006f27300000) (1024K)]
  0x0000019170558260 WorkerThread "GC Thread#1"                     [id=22792, stack(0x0000006f28500000,0x0000006f28600000) (1024K)]
  0x00000191704b1b00 WorkerThread "GC Thread#2"                     [id=23040, stack(0x0000006f28600000,0x0000006f28700000) (1024K)]
  0x000001917060bb60 WorkerThread "GC Thread#3"                     [id=7252, stack(0x0000006f28700000,0x0000006f28800000) (1024K)]
  0x000001917060bf00 WorkerThread "GC Thread#4"                     [id=14340, stack(0x0000006f28800000,0x0000006f28900000) (1024K)]
  0x00000191705b9940 WorkerThread "GC Thread#5"                     [id=20816, stack(0x0000006f28900000,0x0000006f28a00000) (1024K)]
  0x00000191705b9ce0 WorkerThread "GC Thread#6"                     [id=2568, stack(0x0000006f28a00000,0x0000006f28b00000) (1024K)]
  0x0000019170089ff0 WorkerThread "GC Thread#7"                     [id=12828, stack(0x0000006f28b00000,0x0000006f28c00000) (1024K)]
  0x0000019153310f00 ConcurrentGCThread "G1 Main Marker"            [id=22052, stack(0x0000006f27300000,0x0000006f27400000) (1024K)]
  0x00000191533120a0 WorkerThread "G1 Conc#0"                       [id=17428, stack(0x0000006f27400000,0x0000006f27500000) (1024K)]
  0x000001917641b5a0 WorkerThread "G1 Conc#1"                       [id=19700, stack(0x0000006f29a00000,0x0000006f29b00000) (1024K)]
  0x000001915336e380 ConcurrentGCThread "G1 Refine#0"               [id=4272, stack(0x0000006f27500000,0x0000006f27600000) (1024K)]
  0x000001916fbc1a80 ConcurrentGCThread "G1 Service"                [id=17952, stack(0x0000006f27600000,0x0000006f27700000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    24169 17696   !   4       com.android.tools.r8.dex.C::k (2746 bytes)
C2 CompilerThread1    24169 17723   !   4       org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader::findClass (36 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000019100000000-0x0000019100c90000-0x0000019100c90000), size 13172736, SharedBaseAddress: 0x0000019100000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019101000000-0x0000019141000000, reserved size: 1073741824
Narrow klass base: 0x0000019100000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 327680K, used 256315K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 74 young (75776K), 3 survivors (3072K)
 Metaspace       used 112748K, committed 115072K, reserved 1179648K
  class space    used 15240K, committed 16384K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HS|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HS|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HS|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|Cm|TAMS 0x0000000086900000| PB 0x0000000086900000| Complete 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|Cm|TAMS 0x0000000086a00000| PB 0x0000000086a00000| Complete 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|Cm|TAMS 0x0000000086d00000| PB 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x000000008704ed60, 0x0000000087100000| 30%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%|HS|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Complete 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%|HS|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HC|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|Cm|TAMS 0x0000000087400000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%|HS|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Complete 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%|HC|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HS|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HS|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HC|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%|HS|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|Cm|TAMS 0x0000000087f00000| PB 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HS|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%|HS|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HS|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HS|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HC|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HS|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HS|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HS|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%|HC|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%|HS|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Complete 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|Cm|TAMS 0x0000000088b00000| PB 0x0000000088b00000| Complete 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%|HS|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Complete 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HC|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HS|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HC|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%|HS|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Complete 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%|HS|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Complete 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%|HS|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%|HS|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Complete 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%|HC|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%|HS|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Complete 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%|HC|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Complete 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%|HC|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Complete 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%|HS|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Complete 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%|HC|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Complete 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%|HS|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Complete 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%|HC|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Complete 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%|HS|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Complete 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%|HC|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Complete 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%|HS|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Complete 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%|HC|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Complete 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%|HS|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Complete 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%|HS|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Complete 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%|HS|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Complete 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%|HS|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Complete 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%|HS|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%|HC|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Complete 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%|HC|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Complete 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%|HS|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%|HC|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Complete 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%|HC|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Complete 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f1b0910, 0x000000008f200000| 68%| E|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000| PB 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000| PB 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000| PB 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000| PB 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000| PB 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000| PB 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000| PB 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000| PB 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| E|CS|TAMS 0x000000008fa00000| PB 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000| PB 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000| PB 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000| PB 0x000000008fd00000| Complete 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| E|CS|TAMS 0x000000008fe00000| PB 0x000000008fe00000| Complete 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| E|CS|TAMS 0x000000008ff00000| PB 0x000000008ff00000| Complete 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| E|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Complete 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| E|CS|TAMS 0x0000000090100000| PB 0x0000000090100000| Complete 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| E|CS|TAMS 0x0000000090200000| PB 0x0000000090200000| Complete 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| E|CS|TAMS 0x0000000090300000| PB 0x0000000090300000| Complete 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| E|CS|TAMS 0x0000000090400000| PB 0x0000000090400000| Complete 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| E|CS|TAMS 0x0000000090500000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| E|CS|TAMS 0x0000000090600000| PB 0x0000000090600000| Complete 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| E|CS|TAMS 0x0000000090700000| PB 0x0000000090700000| Complete 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| E|CS|TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| E|CS|TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| E|CS|TAMS 0x0000000090a00000| PB 0x0000000090a00000| Complete 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| E|CS|TAMS 0x0000000090b00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| E|CS|TAMS 0x0000000090c00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| E|CS|TAMS 0x0000000090d00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| E|CS|TAMS 0x0000000090e00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| E|CS|TAMS 0x0000000090f00000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| E|CS|TAMS 0x0000000091000000| PB 0x0000000091000000| Complete 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| E|CS|TAMS 0x0000000091100000| PB 0x0000000091100000| Complete 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| E|CS|TAMS 0x0000000091200000| PB 0x0000000091200000| Complete 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| E|CS|TAMS 0x0000000091300000| PB 0x0000000091300000| Complete 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| E|CS|TAMS 0x0000000091400000| PB 0x0000000091400000| Complete 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| E|CS|TAMS 0x0000000091500000| PB 0x0000000091500000| Complete 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| E|CS|TAMS 0x0000000091600000| PB 0x0000000091600000| Complete 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| E|CS|TAMS 0x0000000091700000| PB 0x0000000091700000| Complete 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| E|CS|TAMS 0x0000000091800000| PB 0x0000000091800000| Complete 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| E|CS|TAMS 0x0000000091900000| PB 0x0000000091900000| Complete 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| E|CS|TAMS 0x0000000091a00000| PB 0x0000000091a00000| Complete 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| E|CS|TAMS 0x0000000091b00000| PB 0x0000000091b00000| Complete 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| E|CS|TAMS 0x0000000091c00000| PB 0x0000000091c00000| Complete 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| E|CS|TAMS 0x0000000091d00000| PB 0x0000000091d00000| Complete 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| E|CS|TAMS 0x0000000091e00000| PB 0x0000000091e00000| Complete 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| E|CS|TAMS 0x0000000091f00000| PB 0x0000000091f00000| Complete 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| E|CS|TAMS 0x0000000092000000| PB 0x0000000092000000| Complete 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%| E|CS|TAMS 0x0000000092100000| PB 0x0000000092100000| Complete 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%| E|CS|TAMS 0x0000000092200000| PB 0x0000000092200000| Complete 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%| E|CS|TAMS 0x0000000092300000| PB 0x0000000092300000| Complete 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%| E|CS|TAMS 0x0000000092400000| PB 0x0000000092400000| Complete 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%| E|CS|TAMS 0x0000000092500000| PB 0x0000000092500000| Complete 
| 294|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%| E|CS|TAMS 0x0000000092600000| PB 0x0000000092600000| Complete 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%| E|CS|TAMS 0x0000000092700000| PB 0x0000000092700000| Complete 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| E|CS|TAMS 0x0000000092800000| PB 0x0000000092800000| Complete 
| 350|0x0000000095e00000, 0x0000000095f00000, 0x0000000095f00000|100%| E|CS|TAMS 0x0000000095e00000| PB 0x0000000095e00000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 
|2027|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| O|  |TAMS 0x00000000feb00000| PB 0x00000000feb00000| Untracked 
|2028|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| O|  |TAMS 0x00000000fec00000| PB 0x00000000fec00000| Untracked 
|2029|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| O|  |TAMS 0x00000000fed00000| PB 0x00000000fed00000| Untracked 
|2030|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| O|  |TAMS 0x00000000fee00000| PB 0x00000000fee00000| Untracked 
|2031|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000| PB 0x00000000fef00000| Complete 
|2032|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| E|CS|TAMS 0x00000000ff000000| PB 0x00000000ff000000| Complete 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000| PB 0x00000000ff100000| Complete 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000| PB 0x00000000ff200000| Complete 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000| PB 0x00000000ff300000| Complete 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000| PB 0x00000000ff400000| Complete 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000| PB 0x00000000ff500000| Complete 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| S|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| S|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| S|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x000001916ae40000,0x000001916b240000] _byte_map_base: 0x000001916aa40000

Marking Bits: (CMBitMap*) 0x0000019153300470
 Bits: [0x000001916b240000, 0x000001916d240000)

Polling page: 0x0000019151240000

Metaspace:

Usage:
  Non-class:     95.26 MB used.
      Class:     14.90 MB used.
       Both:    110.16 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      96.38 MB ( 75%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      16.00 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     112.38 MB ( 10%) committed. 

Chunk freelists:
   Non-Class:  15.00 MB
       Class:  15.94 MB
        Both:  30.94 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 185.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 4098.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1793.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 9141.
num_chunk_merges: 6.
num_chunk_splits: 5989.
num_chunks_enlarged: 3810.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=9671Kb max_used=10126Kb free=110328Kb
 bounds [0x00000191631e0000, 0x0000019163be0000, 0x000001916a710000]
CodeHeap 'profiled nmethods': size=120000Kb used=25859Kb max_used=27579Kb free=94140Kb
 bounds [0x000001915b710000, 0x000001915d200000, 0x0000019162c40000]
CodeHeap 'non-nmethods': size=5760Kb used=2959Kb max_used=3020Kb free=2800Kb
 bounds [0x0000019162c40000, 0x0000019162f40000, 0x00000191631e0000]
 total_blobs=15130 nmethods=14097 adapters=937
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 24.081 Thread 0x000001916fd64a90 nmethod 17715 0x000001915b731410 code [0x000001915b7315e0, 0x000001915b731b38]
Event: 24.082 Thread 0x000001916fd64a90 17716       3       java.lang.invoke.BootstrapMethodInvoker::maybeReBoxElements (24 bytes)
Event: 24.082 Thread 0x000001916fd64a90 nmethod 17716 0x000001915bdcfb10 code [0x000001915bdcfd00, 0x000001915bdd0340]
Event: 24.083 Thread 0x000001916fd64a90 17719       3       java.util.LinkedHashMap::keysToArray (7 bytes)
Event: 24.084 Thread 0x000001916fd64a90 nmethod 17719 0x000001915bdb0610 code [0x000001915bdb07c0, 0x000001915bdb0900]
Event: 24.084 Thread 0x000001916fd64a90 17717       3       jdk.internal.org.objectweb.asm.SymbolTable::addConstantIntegerOrFloat (100 bytes)
Event: 24.085 Thread 0x000001916fd64a90 nmethod 17717 0x000001915c99a790 code [0x000001915c99a9a0, 0x000001915c99aef0]
Event: 24.085 Thread 0x000001916fd64a90 17720       3       java.lang.invoke.InfoFromMemberName::getDeclaringClass (8 bytes)
Event: 24.085 Thread 0x000001916fd64a90 nmethod 17720 0x000001915bf6fa90 code [0x000001915bf6fc20, 0x000001915bf6fd50]
Event: 24.085 Thread 0x000001916fd64a90 17718       3       java.lang.invoke.LambdaMetafactory::altMetafactory (392 bytes)
Event: 24.086 Thread 0x000001916fd64a90 nmethod 17718 0x000001915b7dd890 code [0x000001915b7ddc80, 0x000001915b7dfa08]
Event: 24.089 Thread 0x0000019176e80b20 nmethod 17707 0x00000191639ccb10 code [0x00000191639cce00, 0x00000191639cea50]
Event: 24.089 Thread 0x0000019176e80b20 17712       4       java.util.LinkedHashMap::keysToArray (80 bytes)
Event: 24.096 Thread 0x0000019176e80b20 nmethod 17712 0x0000019163886d10 code [0x0000019163886ec0, 0x0000019163887328]
Event: 24.096 Thread 0x0000019176e80b20 17711       4       java.lang.invoke.MethodType::toMethodDescriptorString (28 bytes)
Event: 24.098 Thread 0x0000019176e80b20 nmethod 17711 0x0000019163abfa10 code [0x0000019163abfba0, 0x0000019163abfd50]
Event: 24.098 Thread 0x0000019176e80b20 17708       4       java.lang.ClassLoader::findClass (9 bytes)
Event: 24.099 Thread 0x0000019176e80b20 nmethod 17708 0x00000191639bd510 code [0x00000191639bd6a0, 0x00000191639bd7a0]
Event: 24.102 Thread 0x0000019176e80b20 17722       4       com.android.tools.r8.dex.s::d (8 bytes)
Event: 24.105 Thread 0x0000019176e80b20 nmethod 17722 0x000001916395c590 code [0x000001916395c720, 0x000001916395c878]

GC Heap History (20 events):
Event: 16.325 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 217088K, used 149990K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 30 young (30720K), 10 survivors (10240K)
 Metaspace       used 92701K, committed 94912K, reserved 1179648K
  class space    used 12792K, committed 13888K, reserved 1048576K
}
Event: 16.333 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 217088K, used 137586K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 92701K, committed 94912K, reserved 1179648K
  class space    used 12792K, committed 13888K, reserved 1048576K
}
Event: 17.973 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 247808K, used 194930K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 48 young (49152K), 5 survivors (5120K)
 Metaspace       used 92951K, committed 95168K, reserved 1179648K
  class space    used 12809K, committed 13888K, reserved 1048576K
}
Event: 17.985 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 247808K, used 151092K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 92951K, committed 95168K, reserved 1179648K
  class space    used 12809K, committed 13888K, reserved 1048576K
}
Event: 18.379 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 247808K, used 212532K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 59 young (60416K), 6 survivors (6144K)
 Metaspace       used 95060K, committed 97216K, reserved 1179648K
  class space    used 12981K, committed 14080K, reserved 1048576K
}
Event: 18.389 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 247808K, used 153600K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 95060K, committed 97216K, reserved 1179648K
  class space    used 12981K, committed 14080K, reserved 1048576K
}
Event: 18.841 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 247808K, used 209920K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 63 young (64512K), 8 survivors (8192K)
 Metaspace       used 96569K, committed 98816K, reserved 1179648K
  class space    used 13125K, committed 14272K, reserved 1048576K
}
Event: 18.848 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 297984K, used 156830K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 96569K, committed 98816K, reserved 1179648K
  class space    used 13125K, committed 14272K, reserved 1048576K
}
Event: 19.608 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 297984K, used 245918K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 93 young (95232K), 3 survivors (3072K)
 Metaspace       used 98398K, committed 100608K, reserved 1179648K
  class space    used 13305K, committed 14400K, reserved 1048576K
}
Event: 19.618 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 297984K, used 167273K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 98398K, committed 100608K, reserved 1179648K
  class space    used 13305K, committed 14400K, reserved 1048576K
}
Event: 20.770 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 297984K, used 253289K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 97 young (99328K), 12 survivors (12288K)
 Metaspace       used 103364K, committed 105536K, reserved 1179648K
  class space    used 13932K, committed 15040K, reserved 1048576K
}
Event: 20.780 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 297984K, used 145315K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 103364K, committed 105536K, reserved 1179648K
  class space    used 13932K, committed 15040K, reserved 1048576K
}
Event: 21.674 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 297984K, used 254883K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 116 young (118784K), 8 survivors (8192K)
 Metaspace       used 104531K, committed 106688K, reserved 1179648K
  class space    used 14083K, committed 15168K, reserved 1048576K
}
Event: 21.685 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 297984K, used 151471K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 104531K, committed 106688K, reserved 1179648K
  class space    used 14083K, committed 15168K, reserved 1048576K
}
Event: 22.462 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 297984K, used 251823K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 118 young (120832K), 14 survivors (14336K)
 Metaspace       used 105691K, committed 107904K, reserved 1179648K
  class space    used 14215K, committed 15296K, reserved 1048576K
}
Event: 22.480 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 297984K, used 155477K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 105691K, committed 107904K, reserved 1179648K
  class space    used 14215K, committed 15296K, reserved 1048576K
}
Event: 23.251 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 297984K, used 285525K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 116 young (118784K), 14 survivors (14336K)
 Metaspace       used 111353K, committed 113664K, reserved 1179648K
  class space    used 14956K, committed 16128K, reserved 1048576K
}
Event: 23.263 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 308224K, used 153353K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 111353K, committed 113664K, reserved 1179648K
  class space    used 14956K, committed 16128K, reserved 1048576K
}
Event: 23.267 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 308224K, used 156425K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 13 survivors (13312K)
 Metaspace       used 111361K, committed 113664K, reserved 1179648K
  class space    used 14956K, committed 16128K, reserved 1048576K
}
Event: 23.271 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 308224K, used 154939K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 111361K, committed 113664K, reserved 1179648K
  class space    used 14956K, committed 16128K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.008 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.041 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.088 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.091 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.096 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.098 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.101 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.343 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.481 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.600 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.604 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 1.580 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.581 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.740 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 1.858 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 24.100 Thread 0x000001917c98d820 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000019163887268 relative=0x00000000000003a8
Event: 24.100 Thread 0x000001917c98d820 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000019163887268 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;Z)[Ljava/lang/Object; @ 67 c2
Event: 24.100 Thread 0x000001917c98d820 DEOPT PACKING pc=0x0000019163887268 sp=0x0000006f2d1fd4a0
Event: 24.100 Thread 0x000001917c98d820 DEOPT UNPACKING pc=0x0000019162c946a2 sp=0x0000006f2d1fd440 mode 2
Event: 24.101 Thread 0x0000019179cc2e30 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000019163887268 relative=0x00000000000003a8
Event: 24.101 Thread 0x0000019179cc2e30 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000019163887268 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;Z)[Ljava/lang/Object; @ 67 c2
Event: 24.101 Thread 0x0000019179cc2e30 DEOPT PACKING pc=0x0000019163887268 sp=0x0000006f313fd440
Event: 24.101 Thread 0x0000019179cc2e30 DEOPT UNPACKING pc=0x0000019162c946a2 sp=0x0000006f313fd3e0 mode 2
Event: 24.102 Thread 0x0000019179cc2e30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000191636010d4 relative=0x0000000000000394
Event: 24.102 Thread 0x0000019179cc2e30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000191636010d4 method=java.util.concurrent.ConcurrentLinkedQueue.offer(Ljava/lang/Object;)Z @ 42 c2
Event: 24.102 Thread 0x0000019179cc2e30 DEOPT PACKING pc=0x00000191636010d4 sp=0x0000006f313fea00
Event: 24.102 Thread 0x0000019179cc2e30 DEOPT UNPACKING pc=0x0000019162c946a2 sp=0x0000006f313fe948 mode 2
Event: 24.103 Thread 0x000001917c98d820 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000019163887268 relative=0x00000000000003a8
Event: 24.103 Thread 0x000001917c98d820 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000019163887268 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;Z)[Ljava/lang/Object; @ 67 c2
Event: 24.103 Thread 0x000001917c98d820 DEOPT PACKING pc=0x0000019163887268 sp=0x0000006f2d1fd2d0
Event: 24.103 Thread 0x000001917c98d820 DEOPT UNPACKING pc=0x0000019162c946a2 sp=0x0000006f2d1fd270 mode 2
Event: 24.103 Thread 0x000001917c98d820 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000019163887268 relative=0x00000000000003a8
Event: 24.103 Thread 0x000001917c98d820 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000019163887268 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;Z)[Ljava/lang/Object; @ 67 c2
Event: 24.103 Thread 0x000001917c98d820 DEOPT PACKING pc=0x0000019163887268 sp=0x0000006f2d1fd260
Event: 24.103 Thread 0x000001917c98d820 DEOPT UNPACKING pc=0x0000019162c946a2 sp=0x0000006f2d1fd200 mode 2

Classes loaded (20 events):
Event: 22.908 Loading class com/sun/jmx/mbeanserver/StandardMBeanIntrospector
Event: 22.908 Loading class com/sun/jmx/mbeanserver/StandardMBeanIntrospector done
Event: 22.909 Loading class sun/reflect/misc/MethodUtil
Event: 22.909 Loading class sun/reflect/misc/MethodUtil done
Event: 22.909 Loading class sun/reflect/misc/MethodUtil$1
Event: 22.909 Loading class sun/reflect/misc/MethodUtil$1 done
Event: 23.014 Loading class java/nio/channels/Channels$WritableByteChannelImpl
Event: 23.014 Loading class java/nio/channels/Channels$WritableByteChannelImpl done
Event: 23.020 Loading class java/util/Collections$UnmodifiableSortedMap
Event: 23.020 Loading class java/util/Collections$UnmodifiableSortedMap done
Event: 23.216 Loading class java/nio/BufferUnderflowException
Event: 23.216 Loading class java/nio/BufferUnderflowException done
Event: 24.023 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 24.023 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done
Event: 24.082 Loading class java/util/AbstractList$RandomAccessSubList
Event: 24.082 Loading class java/util/AbstractList$SubList
Event: 24.083 Loading class java/util/AbstractList$SubList done
Event: 24.083 Loading class java/util/AbstractList$RandomAccessSubList done
Event: 24.085 Loading class java/util/AbstractList$SubList$1
Event: 24.085 Loading class java/util/AbstractList$SubList$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 22.954 Thread 0x000001916ff91280 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d408d08}> (0x000000008d408d08) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.954 Thread 0x000001916ff91280 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d409c00}> (0x000000008d409c00) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.954 Thread 0x000001916ff91280 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d409d98}> (0x000000008d409d98) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.955 Thread 0x000001916ff91280 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d40b158}> (0x000000008d40b158) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.969 Thread 0x000001916ff92cc0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d4f0358}> (0x000000008d4f0358) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.969 Thread 0x000001916ff92cc0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d4f1538}> (0x000000008d4f1538) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.969 Thread 0x000001916ff92cc0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d4f1708}> (0x000000008d4f1708) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.974 Thread 0x000001916ff92cc0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d368610}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d368610) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 22.991 Thread 0x000001916ff92cc0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008d2dacc0}> (0x000000008d2dacc0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 23.243 Thread 0x0000019179cc7cf0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008bf057f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java
Event: 24.027 Thread 0x000001917c98e540 Implicit null exception at 0x00000191635cb78b to 0x00000191635cb84c
Event: 24.027 Thread 0x000001917c98e540 Implicit null exception at 0x0000019163bb158b to 0x0000019163bb1f18
Event: 24.027 Thread 0x000001917c98d820 Implicit null exception at 0x0000019163202907 to 0x00000191632029cc
Event: 24.028 Thread 0x000001917c98d820 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008f8cd4d8}: Found class java.lang.Object, but interface was expected> (0x000000008f8cd4d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 24.028 Thread 0x000001917e0131c0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008f8bcea0}: Found class java.lang.Object, but interface was expected> (0x000000008f8bcea0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 24.087 Thread 0x0000019179cc2e30 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f440af8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008f440af8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 24.087 Thread 0x000001917e0131c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f4714a0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008f4714a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 24.087 Thread 0x000001917c98d820 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f58a2c8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008f58a2c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 24.105 Thread 0x000001917c98d820 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008f22b7c0}: Found class java.lang.Object, but interface was expected> (0x000000008f22b7c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 24.106 Thread 0x000001917c98d820 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008f22ef00}: Found class java.lang.Object, but interface was expected> (0x000000008f22ef00) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 22.823 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 22.823 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 22.912 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 22.913 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 22.922 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 22.922 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 22.983 Executing VM operation: ICBufferFull
Event: 22.983 Executing VM operation: ICBufferFull done
Event: 23.003 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.004 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.120 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.120 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.251 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 23.263 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 23.267 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 23.272 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 23.379 Executing VM operation: G1PauseRemark
Event: 23.391 Executing VM operation: G1PauseRemark done
Event: 23.443 Executing VM operation: G1PauseCleanup
Event: 23.443 Executing VM operation: G1PauseCleanup done

Events (20 events):
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163343710
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163377310
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163355110
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163317890
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x00000191632e2890
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x00000191632e1b90
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163298910
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163269f10
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163285c10
Event: 23.390 Thread 0x000001916fc859c0 flushing nmethod 0x0000019163204e10
Event: 23.424 Thread 0x000001917c47c3d0 Thread added: 0x000001917c47c3d0
Event: 23.792 Thread 0x000001917c47c3d0 Thread exited: 0x000001917c47c3d0
Event: 24.025 Thread 0x000001917c98d820 Thread added: 0x000001917c98d820
Event: 24.025 Thread 0x000001917c98e540 Thread added: 0x000001917c98e540
Event: 24.025 Thread 0x000001917c990610 Thread added: 0x000001917c990610
Event: 24.026 Thread 0x0000019179cc2110 Thread added: 0x0000019179cc2110
Event: 24.026 Thread 0x0000019179cc2e30 Thread added: 0x0000019179cc2e30
Event: 24.027 Thread 0x0000019179c5d600 Thread added: 0x0000019179c5d600
Event: 24.027 Thread 0x000001917e0131c0 Thread added: 0x000001917e0131c0
Event: 24.027 Thread 0x000001917e011780 Thread added: 0x000001917e011780


Dynamic libraries:
0x00007ff6d9320000 - 0x00007ff6d9330000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007fffbd670000 - 0x00007fffbd868000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffbd310000 - 0x00007fffbd3d2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffbb2f0000 - 0x00007fffbb5e6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffbae80000 - 0x00007fffbaf80000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff9e270000 - 0x00007fff9e28b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007fffa6650000 - 0x00007fffa6669000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007fffbc4a0000 - 0x00007fffbc551000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffbd0e0000 - 0x00007fffbd17e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffbd590000 - 0x00007fffbd62f000 	C:\WINDOWS\System32\sechost.dll
0x00007fffbd3e0000 - 0x00007fffbd503000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffbae50000 - 0x00007fffbae77000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fffbc630000 - 0x00007fffbc7cd000 	C:\WINDOWS\System32\USER32.dll
0x00007fffbae20000 - 0x00007fffbae42000 	C:\WINDOWS\System32\win32u.dll
0x00007fffaa920000 - 0x00007fffaabba000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007fffbcf00000 - 0x00007fffbcf2b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffbad00000 - 0x00007fffbae19000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffbb5f0000 - 0x00007fffbb68d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fffb29a0000 - 0x00007fffb29aa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffbb720000 - 0x00007fffbb74f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fffb70d0000 - 0x00007fffb70dc000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007fff8fdb0000 - 0x00007fff8fe3e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007fff62790000 - 0x00007fff634a7000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007fffbc8b0000 - 0x00007fffbc91b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fffbab60000 - 0x00007fffbabab000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fffafb00000 - 0x00007fffafb27000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffbab40000 - 0x00007fffbab52000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fffb9570000 - 0x00007fffb9582000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fffb41d0000 - 0x00007fffb41da000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007fffb8b50000 - 0x00007fffb8d51000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffa9960000 - 0x00007fffa9994000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffbb050000 - 0x00007fffbb0d2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffa6350000 - 0x00007fffa635f000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007fff9e250000 - 0x00007fff9e26f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007fffbbab0000 - 0x00007fffbc21e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffb8d60000 - 0x00007fffb9503000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffbb750000 - 0x00007fffbbaa3000 	C:\WINDOWS\System32\combase.dll
0x00007fffba660000 - 0x00007fffba68b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007fffbc560000 - 0x00007fffbc62d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fffbc3f0000 - 0x00007fffbc49d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffbd2b0000 - 0x00007fffbd30b000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffbac30000 - 0x00007fffbac55000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff8e940000 - 0x00007fff8ea17000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007fff9a350000 - 0x00007fff9a368000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007fffb2d80000 - 0x00007fffb2d90000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007fffb6ef0000 - 0x00007fffb6ffa000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffba3c0000 - 0x00007fffba42a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff9cc10000 - 0x00007fff9cc26000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007fffb7d40000 - 0x00007fffb7d50000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007fff91960000 - 0x00007fff91987000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007fff86f80000 - 0x00007fff870c4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007fffaa640000 - 0x00007fffaa64a000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007fffaa5d0000 - 0x00007fffaa5db000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007fffbb690000 - 0x00007fffbb698000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fffba5b0000 - 0x00007fffba5c8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffb9ce0000 - 0x00007fffb9d18000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffbabb0000 - 0x00007fffbabde000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffba5d0000 - 0x00007fffba5dc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffba0a0000 - 0x00007fffba0db000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffbca20000 - 0x00007fffbca28000 	C:\WINDOWS\System32\NSI.dll
0x00007fffa6360000 - 0x00007fffa6369000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007fffa98d0000 - 0x00007fffa98de000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007fffbb190000 - 0x00007fffbb2ed000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fffba6d0000 - 0x00007fffba6f7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fffba690000 - 0x00007fffba6cb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fffa0080000 - 0x00007fffa0087000 	C:\WINDOWS\system32\wshunix.dll
0x00007fffb9f30000 - 0x00007fffb9f63000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 9:40 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (3940M free)
TotalPageFile size 22476M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 666M, peak: 666M
current process commit charge ("private bytes"): 711M, peak: 712M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
