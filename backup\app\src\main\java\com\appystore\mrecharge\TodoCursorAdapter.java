package com.appystore.mrecharge;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.database.Cursor;
import android.util.Log; // Import Log
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CursorAdapter;
import android.widget.TextView; // Assuming you have TextViews in item_todo.xml

// Import your DbHelper if column constants are defined there
// import static com.appystore.mrecharge.DbHelper.*;


public class TodoCursorAdapter extends CursorAdapter {

    private static final String TAG = "TodoCursorAdapter"; // For logging

    // Removed: LayoutInflater inflater; - Not needed with LayoutInflater.from()

    // Constructor remains the same
    public TodoCursorAdapter(Context context, Cursor cursor) {
        // The '0' flag is deprecated, use FLAG_REGISTER_CONTENT_OBSERVER if needed, otherwise remove.
        // super(context, cursor, 0);
        super(context, cursor, FLAG_REGISTER_CONTENT_OBSERVER); // Recommended flag
    }

    /**
     * Makes a new view to hold the data pointed to by cursor.
     * Should only inflate the layout and create ViewHolder here.
     */
    @Override
    public View newView(final Context context, final Cursor cursor, ViewGroup parent) {
        // Inflate the view using LayoutInflater.from()
        View view = LayoutInflater.from(context).inflate(R.layout.item_todo, parent, false);

        // --- Setting OnClickListener in newView ---
        // Note: It's more common to set listeners in bindView or use a ViewHolder pattern
        // But fixing the current structure:
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Get column indices safely inside onClick, using the 'cursor' passed to newView
                int oneColIdx = cursor.getColumnIndex("one");
                int twoColIdx = cursor.getColumnIndex("two");
                int threeColIdx = cursor.getColumnIndex("three");
                int fourColIdx = cursor.getColumnIndex("four"); // Assuming "4rd" was a typo for "four"
                int fiveColIdx = cursor.getColumnIndex("five");
                int numberColIdx = cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_NUMBER); // Use constant
                int amountColIdx = cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_AMOUNT); // Use constant
                int jobColIdx = cursor.getColumnIndex("job");
                int orderIdColIdx = cursor.getColumnIndex("orderid");
                int apiResponseColIdx = cursor.getColumnIndex("apiresponse");

                StringBuilder sb = new StringBuilder();

                // Append data safely, checking each index
                sb.append("1st Command: ");
                sb.append(oneColIdx != -1 ? cursor.getString(oneColIdx) : "[N/A]");
                sb.append("\n2nd Command: "); // Corrected spacing
                sb.append(twoColIdx != -1 ? cursor.getString(twoColIdx) : "[N/A]");
                sb.append("\n3rd Command: "); // Corrected spacing
                sb.append(threeColIdx != -1 ? cursor.getString(threeColIdx) : "[N/A]");
                sb.append("\n4th Command: "); // Corrected typo "4rd" and spacing
                sb.append(fourColIdx != -1 ? cursor.getString(fourColIdx) : "[N/A]");
                sb.append("\n5th Command: "); // Corrected spacing
                sb.append(fiveColIdx != -1 ? cursor.getString(fiveColIdx) : "[N/A]");
                sb.append("\nNumber: "); // Corrected spacing
                sb.append(numberColIdx != -1 ? cursor.getString(numberColIdx) : "[N/A]");
                sb.append("\nAmount: "); // Corrected spacing
                sb.append(amountColIdx != -1 ? cursor.getString(amountColIdx) : "[N/A]");
                sb.append("\nComplete step: "); // Corrected spacing
                sb.append(jobColIdx != -1 ? cursor.getString(jobColIdx) : "[N/A]"); // Assuming job is string
                sb.append("\nOrder Id: "); // Corrected spacing
                sb.append(orderIdColIdx != -1 ? cursor.getString(orderIdColIdx) : "[N/A]");
                sb.append("\nResponse: "); // Corrected spacing
                sb.append(apiResponseColIdx != -1 ? cursor.getString(apiResponseColIdx) : "[N/A]");

                // Show the alert
                showAlert(sb.toString(), context);
            }
        });
        return view;
    }

    /**
     * Bind an existing view to the data pointed to by cursor.
     * This is where you should populate the views (TextViews, etc.)
     */
    @Override
    public void bindView(View view, Context context, Cursor cursor) {
        // --- Example: Populate TextViews (assuming they exist in R.layout.item_todo) ---
        // TextView tvNumber = view.findViewById(R.id.textViewItemNumber); // Replace with actual ID
        // TextView tvAmount = view.findViewById(R.id.textViewItemAmount); // Replace with actual ID
        // TextView tvStatus = view.findViewById(R.id.textViewItemStatus); // Replace with actual ID

        int numberColIdx = cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_NUMBER);
        int amountColIdx = cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_AMOUNT);
        int statusColIdx = cursor.getColumnIndex("status"); // Assuming 'status' column exists

        // Safely set text
        // if (tvNumber != null && numberColIdx != -1) {
        //     tvNumber.setText(cursor.getString(numberColIdx));
        // } else if (tvNumber != null) {
        //     tvNumber.setText("[N/A]");
        // }
        //
        // if (tvAmount != null && amountColIdx != -1) {
        //     tvAmount.setText(cursor.getString(amountColIdx));
        // } else if (tvAmount != null) {
        //     tvAmount.setText("[N/A]");
        // }
        //
        // // Example of using the status
        int status = -1; // Default invalid status
        if (statusColIdx != -1) {
            status = cursor.getInt(statusColIdx);
        } else {
            Log.w(TAG, "bindView: 'status' column not found in cursor.");
        }
        //
        // if (tvStatus != null) {
        //     tvStatus.setText("Status: " + status); // Display status
        //     // Optionally change background or text color based on status
        //     if (status == 0) {
        //          view.setBackgroundColor(context.getResources().getColor(android.R.color.holo_orange_light)); // Example
        //     } else if (status == 1) {
        //          view.setBackgroundColor(context.getResources().getColor(android.R.color.holo_green_light)); // Example
        //     } else {
        //          view.setBackgroundColor(context.getResources().getColor(android.R.color.darker_gray)); // Example
        //     }
        // }

        // Note: The original code had a status check in newView that didn't do anything.
        // The logic to *use* the status (e.g., change appearance) should be here in bindView.
    }

    /* access modifiers changed from: private */
    // Method to show alert (corrected duplicate setMessage)
    private void showAlert(String message, Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null, cannot show alert.");
            return;
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("Details") // Set title
                .setMessage(message) // Set message ONCE
                .setCancelable(false) // Or true if you want it cancelable
                .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialogInterface, int i) {
                        dialogInterface.dismiss(); // Good practice to dismiss the dialog
                    }
                });
        AlertDialog alert = builder.create();
        alert.show();
    }
}