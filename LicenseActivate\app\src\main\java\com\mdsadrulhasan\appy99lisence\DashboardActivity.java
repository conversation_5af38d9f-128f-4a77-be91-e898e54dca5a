package com.mdsadrulhasan.appy99lisence;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.mdsadrulhasan.appy99lisence.recharge.PermissionHelper;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeHistoryActivity;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeService;
import com.mdsadrulhasan.appy99lisence.recharge.RechargeSettingsActivity;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomDialFunction;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomService;
import com.mdsadrulhasan.appy99lisence.fragments.DashboardHomeFragment;
import com.mdsadrulhasan.appy99lisence.fragments.DashboardRechargeFragment;
import com.mdsadrulhasan.appy99lisence.fragments.DashboardSettingsFragment;
import com.mdsadrulhasan.appy99lisence.fragments.DashboardMonitorFragment;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DashboardActivity extends AppCompatActivity {

    private static final String TAG = "DashboardActivity";

    // Intent extra keys
    public static final String EXTRA_LICENSE_KEY = "license_key";
    public static final String EXTRA_DOMAIN_URL = "domain_url";
    public static final String EXTRA_ACTIVATION_STATUS = "activation_status";
    public static final String EXTRA_EXPIRATION_TIMESTAMP = "expiration_timestamp";
    public static final String EXTRA_DEVICE_ID = "device_id";
    public static final String EXTRA_DEVICE_INFO = "device_info";

    // UI Components - Navigation
    private LinearLayout navHome;
    private LinearLayout navBanking;
    private LinearLayout navSettings;
    private LinearLayout navMonitor;
    private FrameLayout contentContainer;

    // Data from Intent
    private String licenseKey;
    private String domainUrl;
    private boolean activationStatus;
    private long expirationTimestamp;
    private String deviceId;
    private String deviceInfo;

    // Fragment management
    private FragmentManager fragmentManager;
    private String currentTab = "home";

    // Functionality
    private SharedPreferences preferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_dashboard);

        // Initialize preferences and fragment manager
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        fragmentManager = getSupportFragmentManager();

        // Initialize UI components
        initializeUI();

        // Get data from Intent
        extractIntentData();

        // Validate received data
        if (validateData()) {
            // Load default tab (Home)
            selectTab("home");
        } else {
            // Handle invalid data
            handleInvalidData();
        }

        Log.d(TAG, "DashboardActivity created");
    }

    /**
     * Initialize UI components
     */
    private void initializeUI() {
        // Initialize navigation components
        navHome = findViewById(R.id.nav_home);
        navBanking = findViewById(R.id.nav_banking);
        navSettings = findViewById(R.id.nav_settings);
        navMonitor = findViewById(R.id.nav_monitor);
        contentContainer = findViewById(R.id.content_container);

        // Set up navigation click listeners
        setupNavigationListeners();
    }

    /**
     * Setup navigation listeners
     */
    private void setupNavigationListeners() {
        if (navHome != null) {
            navHome.setOnClickListener(v -> selectTab("home"));
        }

        if (navBanking != null) {
            navBanking.setOnClickListener(v -> selectTab("banking"));
        }

        if (navSettings != null) {
            navSettings.setOnClickListener(v -> selectTab("settings"));
        }

        if (navMonitor != null) {
            navMonitor.setOnClickListener(v -> selectTab("monitor"));
        }
    }

    /**
     * Select and display a tab
     */
    private void selectTab(String tabName) {
        if (tabName.equals(currentTab)) {
            return; // Already selected
        }

        currentTab = tabName;
        updateTabSelection();
        loadFragment(tabName);
    }

    /**
     * Update visual tab selection
     */
    private void updateTabSelection() {
        // Reset all tabs to default background
        if (navHome != null) navHome.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        if (navBanking != null) navBanking.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        if (navSettings != null) navSettings.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        if (navMonitor != null) navMonitor.setBackgroundColor(getResources().getColor(android.R.color.transparent));

        // Highlight selected tab
        switch (currentTab) {
            case "home":
                if (navHome != null) navHome.setBackgroundColor(getResources().getColor(R.color.selected_tab_color, null));
                break;
            case "banking":
                if (navBanking != null) navBanking.setBackgroundColor(getResources().getColor(R.color.selected_tab_color, null));
                break;
            case "settings":
                if (navSettings != null) navSettings.setBackgroundColor(getResources().getColor(R.color.selected_tab_color, null));
                break;
            case "monitor":
                if (navMonitor != null) navMonitor.setBackgroundColor(getResources().getColor(R.color.selected_tab_color, null));
                break;
        }
    }

    /**
     * Load fragment for selected tab
     */
    private void loadFragment(String tabName) {
        Fragment fragment = null;

        switch (tabName) {
            case "home":
                fragment = DashboardHomeFragment.newInstance(licenseKey, domainUrl, activationStatus,
                    expirationTimestamp, deviceId, deviceInfo);
                break;
            case "banking":
                fragment = DashboardRechargeFragment.newInstance(licenseKey, deviceId, domainUrl);
                break;
            case "settings":
                fragment = DashboardSettingsFragment.newInstance();
                break;
            case "monitor":
                fragment = DashboardMonitorFragment.newInstance();
                break;
        }

        if (fragment != null && contentContainer != null) {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.replace(R.id.content_container, fragment);
            transaction.commit();
        }
    }

    /**
     * Extract data from Intent extras
     */
    private void extractIntentData() {
        Intent intent = getIntent();

        licenseKey = intent.getStringExtra(EXTRA_LICENSE_KEY);
        domainUrl = intent.getStringExtra(EXTRA_DOMAIN_URL);
        activationStatus = intent.getBooleanExtra(EXTRA_ACTIVATION_STATUS, false);
        expirationTimestamp = intent.getLongExtra(EXTRA_EXPIRATION_TIMESTAMP, 0);
        deviceId = intent.getStringExtra(EXTRA_DEVICE_ID);
        deviceInfo = intent.getStringExtra(EXTRA_DEVICE_INFO);

        Log.d(TAG, "Received data - License: " + (licenseKey != null ? licenseKey.substring(0, Math.min(licenseKey.length(), 8)) + "..." : "null") +
                   ", Domain: " + domainUrl + ", Activated: " + activationStatus);
    }

    /**
     * Validate received data
     */
    private boolean validateData() {
        if (licenseKey == null || licenseKey.isEmpty()) {
            Log.e(TAG, "Invalid license key received");
            return false;
        }

        if (domainUrl == null || domainUrl.isEmpty()) {
            Log.e(TAG, "Invalid domain URL received");
            return false;
        }

        if (!activationStatus) {
            Log.e(TAG, "License not activated");
            return false;
        }

        if (expirationTimestamp <= 0) {
            Log.w(TAG, "Invalid expiration timestamp");
            // Don't fail validation for this, just log warning
        }

        return true;
    }



    /**
     * Handle invalid data scenario
     */
    private void handleInvalidData() {
        Toast.makeText(this, "❌ Invalid authentication data. Returning to main screen.", Toast.LENGTH_LONG).show();

        Log.e(TAG, "Invalid data received, showing error state");

        // Auto-close after a delay
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                finish();
            }
        }, 3000); // Close after 3 seconds
    }



    @Override
    protected void onResume() {
        super.onResume();
        // Save credentials for fragments to use
        saveCredentials();
    }

    /**
     * Save credentials for fragments to use
     */
    private void saveCredentials() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString("license_key", licenseKey);
        editor.putString("device_id", deviceId);
        editor.putString("domain_url", domainUrl);
        editor.putBoolean("activation_status", activationStatus);
        editor.putLong("expiration_timestamp", expirationTimestamp);
        editor.putString("device_info", deviceInfo);
        editor.apply();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        Log.d(TAG, "Back button pressed, returning to MainActivity");
    }
}
