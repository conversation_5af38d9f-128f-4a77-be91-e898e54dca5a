package com.appystore.mrecharge.activity;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ToggleButton;
import androidx.appcompat.app.AppCompatActivity;

import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.R;

/* loaded from: classes.dex */
public class intsetting extends AppCompatActivity {
    private static final int PERMISSION_REQUEST_CODE = 1;
    private String ACCESSIBILITY_SERVICE_NAME;
    private DbHelper mydb;
    private RadioButton radioButton;
    private RadioGroup radioGroup;
    private RadioGroup radioGroup2;
    private RadioGroup radioGroup3;
    String servname;
    String slot;
    private static final String[] distic = {"No Service", "Bkash", "Rocket", "Nogad", "Surecash"};
    private static final String[] bi = {"OFF", "ON"};

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.settingsd);
        ToggleButton toggleButton = (ToggleButton) findViewById(R.id.bkash);
        ToggleButton toggleButton2 = (ToggleButton) findViewById(R.id.rocket);
        ToggleButton toggleButton3 = (ToggleButton) findViewById(R.id.nogad);
        ToggleButton toggleButton4 = (ToggleButton) findViewById(R.id.upay);
        ToggleButton toggleButton5 = (ToggleButton) findViewById(R.id.bill);
        LinearLayout linearLayout = (LinearLayout) findViewById(R.id.home);
        LinearLayout linearLayout2 = (LinearLayout) findViewById(R.id.sms);
        SharedPreferences sharedPreferences = getSharedPreferences("serv", 0);
        if (sharedPreferences.getInt("sima_servicem", 0) == 1) {
            toggleButton.setChecked(true);
        } else {
            toggleButton.setChecked(false);
        }
        if (sharedPreferences.getInt("simb_servicem", 0) == 1) {
            toggleButton2.setChecked(true);
        } else {
            toggleButton2.setChecked(false);
        }
        if (sharedPreferences.getInt("simb_servicem3", 0) == 1) {
            toggleButton3.setChecked(true);
        } else {
            toggleButton3.setChecked(false);
        }
        if (sharedPreferences.getInt("simb_servicem4", 0) == 1) {
            toggleButton4.setChecked(true);
        } else {
            toggleButton4.setChecked(false);
        }
        if (sharedPreferences.getInt("bill", 0) == 1) {
            toggleButton5.setChecked(true);
        } else {
            toggleButton5.setChecked(false);
        }
        toggleButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.1
            @Override // android.widget.CompoundButton.OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i;
                if (z) {
                    i = 1;
                    intsetting.this.servname = "BK";
                } else {
                    intsetting.this.servname = "NO";
                    i = 0;
                }
                SharedPreferences.Editor edit = intsetting.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("sima_servicem", i);
                edit.commit();
                intsetting intsettingVar = intsetting.this;
                intsettingVar.SavePreferences("m4", intsettingVar.servname);
            }
        });
        toggleButton2.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.2
            @Override // android.widget.CompoundButton.OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i;
                if (z) {
                    i = 1;
                    intsetting.this.servname = "RK";
                } else {
                    intsetting.this.servname = "NO";
                    i = 0;
                }
                SharedPreferences.Editor edit = intsetting.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simb_servicem", i);
                edit.commit();
                intsetting intsettingVar = intsetting.this;
                intsettingVar.SavePreferences("m5", intsettingVar.servname);
            }
        });
        toggleButton3.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.3
            @Override // android.widget.CompoundButton.OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i;
                if (z) {
                    i = 1;
                    intsetting.this.servname = "NG";
                } else {
                    intsetting.this.servname = "NO";
                    i = 0;
                }
                SharedPreferences.Editor edit = intsetting.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simb_servicem3", i);
                edit.commit();
                intsetting intsettingVar = intsetting.this;
                intsettingVar.SavePreferences("m6", intsettingVar.servname);
            }
        });
        toggleButton4.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.4
            @Override // android.widget.CompoundButton.OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i;
                if (z) {
                    i = 1;
                    intsetting.this.servname = "UP";
                } else {
                    intsetting.this.servname = "NO";
                    i = 0;
                }
                SharedPreferences.Editor edit = intsetting.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simb_servicem4", i);
                edit.commit();
                intsetting intsettingVar = intsetting.this;
                intsettingVar.SavePreferences("m7", intsettingVar.servname);
            }
        });
        toggleButton5.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.5
            @Override // android.widget.CompoundButton.OnCheckedChangeListener
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i;
                if (z) {
                    i = 1;
                    intsetting.this.servname = "BILL";
                } else {
                    intsetting.this.servname = "NO";
                    i = 0;
                }
                SharedPreferences.Editor edit = intsetting.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("bill", i);
                edit.commit();
                intsetting intsettingVar = intsetting.this;
                intsettingVar.SavePreferences("m8", intsettingVar.servname);
            }
        });
        linearLayout2.setOnClickListener(new View.OnClickListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.6
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                intsetting.this.startActivity(new Intent(intsetting.this, (Class<?>) Settings.class));
            }
        });
        linearLayout.setOnClickListener(new View.OnClickListener() { // from class: com.flexisoftwarebd.serverpro.activity.intsetting.7
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                intsetting.this.startActivity(new Intent(intsetting.this, (Class<?>) MainActivity.class));
            }
        });
    }

    public static String getPref(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "example.com");
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }
}