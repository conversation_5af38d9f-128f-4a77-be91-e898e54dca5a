<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/activity_main"
    android:background="@drawable/bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:id="@+id/btnSpeakContainer"
        android:paddingTop="0dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/home"
            android:background="@drawable/rightorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_home_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/bank"
            android:background="@drawable/rightorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_settings_cell_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="M Banking"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/sms"
            android:background="@drawable/select"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_settings_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"/>
        </LinearLayout>
    </LinearLayout>
    <TextView
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#ffffffff"
        android:gravity="center_horizontal"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:text="Forward Rquest to sms"/>
    <ScrollView
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="1dp"
        android:layout_marginRight="1dp">
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/rounded_corner"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Bkash"/>
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <CheckBox
                    android:textColor="#ff276ca4"
                    android:id="@+id/bks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:checked="false"
                    android:text=""
                    android:onClick="checkbox_clicked"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/bkash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter number here"/>
            </LinearLayout>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Rocket"/>
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <CheckBox
                    android:textColor="#ff276ca4"
                    android:id="@+id/rks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:checked="false"
                    android:text=""
                    android:onClick="checkbox_clicked"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/rocket"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter number here"/>
            </LinearLayout>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Nogad"/>
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <CheckBox
                    android:textColor="#ff276ca4"
                    android:id="@+id/ngs"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:checked="false"
                    android:text=""
                    android:onClick="checkbox_clicked"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/nogad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter number here"/>
            </LinearLayout>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Request Every sec"/>
            <EditText
                android:textSize="16sp"
                android:textColor="#ff3c3adb"
                android:textColorHint="#ff6722e0"
                android:id="@+id/time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:hint="0 sec"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Maximum Try"/>
            <EditText
                android:textSize="16sp"
                android:textColor="#ff3c3adb"
                android:textColorHint="#ff6722e0"
                android:id="@+id/trys"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:hint="0"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:id="@+id/blb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Dial function"/>
            <Spinner
                android:id="@+id/dc"
                android:layout_width="200dp"
                android:layout_height="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:layout_below="@+id/blb"/>
            <Button
                android:textColor="#ffffffff"
                android:layout_gravity="center_horizontal"
                android:id="@+id/savedata"
                android:background="@drawable/save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="Save"/>

            <!-- API URL Configuration Section -->
            <TextView
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:text="API Configuration"/>

            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:textColor="#ff2cab0e"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="License API URL"/>

            <EditText
                android:textSize="14sp"
                android:textColor="#ff3c3adb"
                android:textColorHint="#ff6722e0"
                android:id="@+id/api_url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:inputType="textUri"
                android:hint="http://192.168.0.106/AppyStoreMRecharge/admin/api/device_auth.php"/>

            <Button
                android:textColor="#ffffffff"
                android:layout_gravity="center_horizontal"
                android:id="@+id/save_api_url"
                android:background="@drawable/save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="20dp"
                android:text="Update API URL"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>