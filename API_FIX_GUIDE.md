# 🔧 API Fix Guide - "Direct access not allowed" Error

## ✅ **Issue Fixed!**

The error "Direct access not allowed" has been resolved. Here's what was fixed and how to test:

## 🛠️ **What Was Fixed:**

### 1. **Access Control Issue**
- **Problem**: The `config.php` file expected `ADMIN_ACCESS` constant but the API was defining `API_ACCESS`
- **Fix**: Updated `admin/api/recharge_api.php` to define `ADMIN_ACCESS` instead

### 2. **Missing Database Tables**
- **Problem**: Recharge tables might not exist in the database
- **Fix**: Added automatic table creation in the API

### 3. **Better Error Handling**
- **Problem**: Limited debugging information
- **Fix**: Added comprehensive error logging and debugging

## 🚀 **How to Test the Fix:**

### Method 1: Use the Test Panel (Recommended)
1. Open your browser and go to: `http://yourserver.com/admin/test_recharge_api.php`
2. This will show you:
   - Database connection status
   - Table existence status
   - API test buttons
   - Manual testing form

### Method 2: Direct API Test
1. Test basic API connection:
   ```
   http://yourserver.com/admin/api/recharge_api.php?action=test_api
   ```

2. Test operators endpoint:
   ```
   http://yourserver.com/admin/api/recharge_api.php?action=get_operators
   ```

### Method 3: Android App Test
1. Build and install the updated app
2. Complete license activation
3. Try to submit a recharge request
4. Check Android Studio Logcat for detailed logs

## 📊 **Expected Results:**

### ✅ **Success Response Example:**
```json
{
    "success": true,
    "message": "Recharge API is working",
    "timestamp": "2024-01-15 10:30:00",
    "database_status": "connected",
    "tables_status": {
        "operators": "exists",
        "recharge_logs": "exists",
        "licenses": "exists",
        "auth_logs": "exists"
    }
}
```

### ❌ **If Still Getting Errors:**

#### Error: "Database connection failed"
**Solution:**
1. Check `admin/config.php` database credentials:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('DB_NAME', 'your_database');
   ```

#### Error: "License not found"
**Solution:**
1. Make sure you have valid license data in the `licenses` table
2. Check that the license status is set to 1 (active)
3. Verify the device is approved in `auth_logs` table

#### Error: "Table doesn't exist"
**Solution:**
1. The API will automatically create tables, but you can also run:
   ```sql
   source admin/setup_recharge_tables.sql
   ```

## 🔍 **Debugging Steps:**

### 1. Check Server Logs
Look for error messages in your server's error log:
```bash
tail -f /var/log/apache2/error.log
# or
tail -f /var/log/nginx/error.log
```

### 2. Check PHP Error Log
Enable error logging in `admin/config.php`:
```php
define('DEBUG_MODE', 1);
```

### 3. Test Database Connection
Use the test panel to verify database connectivity and table status.

### 4. Verify File Permissions
Ensure the API file has proper permissions:
```bash
chmod 644 admin/api/recharge_api.php
chmod 644 admin/config.php
```

## 📱 **Android App Configuration:**

### Update API URL
In `RechargeApiClient.java`, update the API URL:
```java
private static final String DEFAULT_API_URL = "http://YOUR_SERVER/admin/api/recharge_api.php";
```

### Test License Data
Make sure your app is sending valid license_key and device_id:
```java
// Check these values in your app
String licenseKey = preferences.getString("license_key", "");
String deviceId = preferences.getString("device_id", "");
```

## ✨ **Verification Checklist:**

- [ ] ✅ API responds without "Direct access not allowed" error
- [ ] ✅ Database connection successful
- [ ] ✅ All required tables exist
- [ ] ✅ Operators endpoint returns data
- [ ] ✅ License validation works (if you have valid license data)
- [ ] ✅ Android app can connect to API
- [ ] ✅ Recharge submission works (with valid license)

## 🎯 **Next Steps:**

1. **Test the API** using the test panel
2. **Verify database setup** and table creation
3. **Test Android app** with valid license data
4. **Submit a recharge request** to test full workflow
5. **Check transaction logs** in the database

## 📞 **If You Still Need Help:**

If you're still experiencing issues:

1. **Share the test panel results** - Visit `admin/test_recharge_api.php` and share the output
2. **Check server error logs** - Look for any PHP or database errors
3. **Verify database credentials** - Ensure `config.php` has correct settings
4. **Test with valid license** - Make sure you have actual license data in the database

The API should now work correctly! 🎉
