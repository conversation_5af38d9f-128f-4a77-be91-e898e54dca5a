package com.mdsadrulhasan.appy99lisence.telecom;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * API client for telecom recharge operations
 * Handles communication with the server for recharge requests and responses
 */
public class TelecomApiClient {
    
    private static final String TAG = "TelecomApiClient";
    
    private Context context;
    private ExecutorService executorService;
    private String baseUrl;
    
    // API endpoints
    private static final String ENDPOINT_FETCH_ORDERS = "fetch_orders";
    private static final String ENDPOINT_SEND_RESPONSE = "send_response";
    private static final String ENDPOINT_UPDATE_STATUS = "update_status";
    
    // Connection timeouts
    private static final int CONNECTION_TIMEOUT = 10000; // 10 seconds
    private static final int READ_TIMEOUT = 15000; // 15 seconds
    
    public TelecomApiClient(Context context) {
        this.context = context;
        this.executorService = Executors.newFixedThreadPool(3);
        
        // Get base URL from preferences (set during license activation)
        String domain = getPreference("domain", "");
        String protocol = getPreference("sec", "http");
        
        if (!domain.isEmpty()) {
            this.baseUrl = protocol + "://" + domain + "/api/telecom.php";
        } else {
            // Fallback URL
            this.baseUrl = "http://192.168.0.106/AppyStoreMRecharge/admin/api/telecom.php";
        }
        
        Log.d(TAG, "TelecomApiClient initialized with URL: " + baseUrl);
    }
    
    /**
     * Fetch pending recharge orders from server
     */
    public void fetchPendingOrders(ApiCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String deviceId = getPreference("device_id", "");
                    String licenseKey = getPreference("license_key", "");
                    
                    String params = "action=" + ENDPOINT_FETCH_ORDERS +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8");
                    
                    String response = makeApiCall(params);
                    
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                    
                } catch (Exception e) {
                    Log.e(TAG, "Error fetching pending orders", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }
    
    /**
     * Send recharge response to server
     */
    public void sendRechargeResponse(String orderId, String response, String status, ApiCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String deviceId = getPreference("device_id", "");
                    String licenseKey = getPreference("license_key", "");
                    
                    String params = "action=" + ENDPOINT_SEND_RESPONSE +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&order_id=" + URLEncoder.encode(orderId, "UTF-8") +
                                  "&response=" + URLEncoder.encode(response, "UTF-8") +
                                  "&status=" + URLEncoder.encode(status, "UTF-8") +
                                  "&timestamp=" + System.currentTimeMillis();
                    
                    String apiResponse = makeApiCall(params);
                    
                    Log.d(TAG, "Recharge response sent to server: " + apiResponse);
                    
                    if (callback != null) {
                        callback.onSuccess(apiResponse);
                    }
                    
                } catch (Exception e) {
                    Log.e(TAG, "Error sending recharge response", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }
    
    /**
     * Update recharge status on server
     */
    public void updateRechargeStatus(String orderId, String status, ApiCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String deviceId = getPreference("device_id", "");
                    String licenseKey = getPreference("license_key", "");
                    
                    String params = "action=" + ENDPOINT_UPDATE_STATUS +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&order_id=" + URLEncoder.encode(orderId, "UTF-8") +
                                  "&status=" + URLEncoder.encode(status, "UTF-8") +
                                  "&timestamp=" + System.currentTimeMillis();
                    
                    String response = makeApiCall(params);
                    
                    Log.d(TAG, "Recharge status updated on server: " + response);
                    
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                    
                } catch (Exception e) {
                    Log.e(TAG, "Error updating recharge status", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }
    
    /**
     * Make HTTP API call
     */
    private String makeApiCall(String params) throws Exception {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(baseUrl);
            connection = (HttpURLConnection) url.openConnection();
            
            // Set request properties
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "LicenseActivate-TelecomClient/1.0");
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setDoOutput(true);
            
            // Send POST data
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.writeBytes(params);
                wr.flush();
            }
            
            // Read response
            int responseCode = connection.getResponseCode();
            Log.d(TAG, "API Response Code: " + responseCode);
            
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
            
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            String responseString = response.toString();
            Log.d(TAG, "API Response: " + responseString);
            
            if (responseCode >= 200 && responseCode < 300) {
                return responseString;
            } else {
                throw new Exception("HTTP " + responseCode + ": " + responseString);
            }
            
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    /**
     * Parse JSON response and extract orders
     */
    public void parseOrdersResponse(String jsonResponse, OrdersCallback callback) {
        try {
            JSONObject response = new JSONObject(jsonResponse);
            
            if (response.has("status") && response.getInt("status") == 1) {
                if (response.has("orders")) {
                    // Process orders array
                    if (callback != null) {
                        callback.onOrdersReceived(response.getJSONArray("orders"));
                    }
                } else {
                    Log.d(TAG, "No orders found in response");
                    if (callback != null) {
                        callback.onNoOrders();
                    }
                }
            } else {
                String message = response.optString("message", "Unknown error");
                Log.e(TAG, "API error: " + message);
                if (callback != null) {
                    callback.onError(message);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing orders response", e);
            if (callback != null) {
                callback.onError(e.getMessage());
            }
        }
    }
    
    /**
     * Get device information for API calls
     */
    private String getDeviceInfo() {
        return android.os.Build.MANUFACTURER + " " + 
               android.os.Build.MODEL + " (Android " + 
               android.os.Build.VERSION.RELEASE + ")";
    }
    
    /**
     * Get preference value
     */
    private String getPreference(String key, String defaultValue) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(key, defaultValue);
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        Log.d(TAG, "TelecomApiClient cleaned up");
    }
    
    /**
     * Callback interface for API responses
     */
    public interface ApiCallback {
        void onSuccess(String response);
        void onError(String error);
    }
    
    /**
     * Callback interface for orders
     */
    public interface OrdersCallback {
        void onOrdersReceived(org.json.JSONArray orders);
        void onNoOrders();
        void onError(String error);
    }
}
