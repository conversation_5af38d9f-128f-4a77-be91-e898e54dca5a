<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Service Status Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Service Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/telecom_service_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Service Status: Checking..."
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <Button
                        android:id="@+id/start_telecom_service_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/button_background"
                        android:text="Start Service"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />

                    <Button
                        android:id="@+id/stop_telecom_service_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/button_background"
                        android:text="Stop Service"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Network Settings -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Network Settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="SIM A:"
                        android:textColor="#3F51B5"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/telecom_sim_a_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/white"
                        android:padding="8dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="SIM B:"
                        android:textColor="#3F51B5"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/telecom_sim_b_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/white"
                        android:padding="8dp" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- System Settings -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="System Settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <!-- SMS Service -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="SMS Service"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff090909" />

                    <ToggleButton
                        android:id="@+id/telecom_sms_service_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>

                <!-- Accessibility -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Accessibility"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/telecom_accessibility_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>

                <!-- Power Optimize -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Power Optimize"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/telecom_power_optimize_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>

                <!-- Auto Start Service -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Auto Start Service"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/telecom_auto_start_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="true"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>
