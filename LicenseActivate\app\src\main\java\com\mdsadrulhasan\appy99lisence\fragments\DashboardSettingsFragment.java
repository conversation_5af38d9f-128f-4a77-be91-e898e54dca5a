package com.mdsadrulhasan.appy99lisence.fragments;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.mdsadrulhasan.appy99lisence.R;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomService;

public class DashboardSettingsFragment extends Fragment {

    private static final String TAG = "DashboardSettingsFragment";

    // UI Components
    private TextView telecomServiceStatusText;
    private Button startServiceButton;
    private Button stopServiceButton;
    private Spinner telecomSimASpinner;
    private Spinner telecomSimBSpinner;
    private ToggleButton telecomSmsServiceToggle;
    private ToggleButton telecomAccessibilityToggle;
    private ToggleButton telecomPowerOptimizeToggle;
    private ToggleButton telecomAutoStartToggle;

    private SharedPreferences preferences;

    public static DashboardSettingsFragment newInstance() {
        return new DashboardSettingsFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        preferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_telecom_settings, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeViews(view);
        setupClickListeners();
        setupSpinners();
        loadSettings();
        updateServiceStatus();
    }

    private void initializeViews(View view) {
        telecomServiceStatusText = view.findViewById(R.id.telecom_service_status_text);
        startServiceButton = view.findViewById(R.id.start_service_button);
        stopServiceButton = view.findViewById(R.id.stop_service_button);
        telecomSimASpinner = view.findViewById(R.id.telecom_sim_a_spinner);
        telecomSimBSpinner = view.findViewById(R.id.telecom_sim_b_spinner);
        telecomSmsServiceToggle = view.findViewById(R.id.telecom_sms_service_toggle);
        telecomAccessibilityToggle = view.findViewById(R.id.telecom_accessibility_toggle);
        telecomPowerOptimizeToggle = view.findViewById(R.id.telecom_power_optimize_toggle);
        telecomAutoStartToggle = view.findViewById(R.id.telecom_auto_start_toggle);
    }

    private void setupClickListeners() {
        if (startServiceButton != null) {
            startServiceButton.setOnClickListener(v -> startTelecomService());
        }

        if (stopServiceButton != null) {
            stopServiceButton.setOnClickListener(v -> stopTelecomService());
        }

        if (telecomSmsServiceToggle != null) {
            telecomSmsServiceToggle.setOnCheckedChangeListener((buttonView, isChecked) -> 
                saveSetting("sms_service_enabled", isChecked));
        }

        if (telecomAccessibilityToggle != null) {
            telecomAccessibilityToggle.setOnCheckedChangeListener((buttonView, isChecked) -> 
                saveSetting("accessibility_enabled", isChecked));
        }

        if (telecomPowerOptimizeToggle != null) {
            telecomPowerOptimizeToggle.setOnCheckedChangeListener((buttonView, isChecked) -> 
                saveSetting("power_optimize_enabled", isChecked));
        }

        if (telecomAutoStartToggle != null) {
            telecomAutoStartToggle.setOnCheckedChangeListener((buttonView, isChecked) -> 
                saveSetting("auto_start_service", isChecked));
        }
    }

    private void setupSpinners() {
        // Setup SIM A spinner
        if (telecomSimASpinner != null) {
            String[] simOptions = {"Auto", "SIM 1", "SIM 2"};
            ArrayAdapter<String> simAAdapter = new ArrayAdapter<>(requireContext(), 
                android.R.layout.simple_spinner_item, simOptions);
            simAAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            telecomSimASpinner.setAdapter(simAAdapter);
        }

        // Setup SIM B spinner
        if (telecomSimBSpinner != null) {
            String[] simOptions = {"Auto", "SIM 1", "SIM 2"};
            ArrayAdapter<String> simBAdapter = new ArrayAdapter<>(requireContext(), 
                android.R.layout.simple_spinner_item, simOptions);
            simBAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            telecomSimBSpinner.setAdapter(simBAdapter);
        }
    }

    private void loadSettings() {
        // Load toggle settings
        if (telecomSmsServiceToggle != null) {
            telecomSmsServiceToggle.setChecked(preferences.getBoolean("sms_service_enabled", true));
        }
        if (telecomAccessibilityToggle != null) {
            telecomAccessibilityToggle.setChecked(preferences.getBoolean("accessibility_enabled", false));
        }
        if (telecomPowerOptimizeToggle != null) {
            telecomPowerOptimizeToggle.setChecked(preferences.getBoolean("power_optimize_enabled", false));
        }
        if (telecomAutoStartToggle != null) {
            telecomAutoStartToggle.setChecked(preferences.getBoolean("auto_start_service", true));
        }

        // Load spinner settings
        if (telecomSimASpinner != null) {
            int simASelection = preferences.getInt("sim_a_selection", 0);
            telecomSimASpinner.setSelection(simASelection);
        }
        if (telecomSimBSpinner != null) {
            int simBSelection = preferences.getInt("sim_b_selection", 0);
            telecomSimBSpinner.setSelection(simBSelection);
        }
    }

    private void saveSetting(String key, boolean value) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(key, value);
        editor.apply();
        
        Log.d(TAG, "Setting saved: " + key + " = " + value);
    }

    private void startTelecomService() {
        try {
            TelecomService.startTelecomService(requireContext());
            updateServiceStatus();
            Log.d(TAG, "Telecom service start requested");
        } catch (Exception e) {
            Log.e(TAG, "Error starting telecom service", e);
        }
    }

    private void stopTelecomService() {
        try {
            TelecomService.stopTelecomService(requireContext());
            updateServiceStatus();
            Log.d(TAG, "Telecom service stop requested");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping telecom service", e);
        }
    }

    private void updateServiceStatus() {
        if (telecomServiceStatusText != null) {
            boolean serviceRunning = preferences.getBoolean("telecom_service_running", false);
            
            String status;
            if (serviceRunning) {
                status = "🟢 Telecom service is running";
                telecomServiceStatusText.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else {
                status = "🔴 Telecom service is stopped";
                telecomServiceStatusText.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }
            
            telecomServiceStatusText.setText(status);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh status when fragment becomes visible
        updateServiceStatus();
    }

    @Override
    public void onPause() {
        super.onPause();
        
        // Save spinner selections
        if (telecomSimASpinner != null) {
            SharedPreferences.Editor editor = preferences.edit();
            editor.putInt("sim_a_selection", telecomSimASpinner.getSelectedItemPosition());
            editor.apply();
        }
        
        if (telecomSimBSpinner != null) {
            SharedPreferences.Editor editor = preferences.edit();
            editor.putInt("sim_b_selection", telecomSimBSpinner.getSelectedItemPosition());
            editor.apply();
        }
    }
}
