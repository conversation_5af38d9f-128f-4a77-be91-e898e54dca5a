package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.mdsadrulhasan.appy99lisence.R;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Activity for configuring recharge settings
 */
public class RechargeSettingsActivity extends AppCompatActivity {
    
    private static final String TAG = "RechargeSettingsActivity";
    
    // UI Components
    private Spinner sim1OperatorSpinner;
    private Spinner sim2OperatorSpinner;
    private CheckBox autoRechargeCheckbox;
    private EditText serverUrlField;
    private EditText apiPinField;
    private EditText checkIntervalField;
    private Button saveSettingsButton;
    private Button backButton;
    private Button startServiceButton;
    private Button stopServiceButton;
    private TextView statusText;
    
    // Data
    private String licenseKey;
    private String deviceId;
    private SharedPreferences preferences;
    private RechargeDbHelper dbHelper;
    private RechargeApiClient apiClient;
    
    // Operators data
    private List<String> operatorNames;
    private List<String> operatorCodes;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recharge_settings);
        
        // Initialize components
        initializeComponents();
        
        // Get intent data
        extractIntentData();
        
        // Initialize UI
        initializeUI();
        
        // Load current settings
        loadCurrentSettings();
        
        // Load operators
        loadOperators();
        
        Log.d(TAG, "RechargeSettingsActivity created");
    }
    
    private void initializeComponents() {
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        dbHelper = new RechargeDbHelper(this);
        apiClient = new RechargeApiClient(this);
        
        operatorNames = new ArrayList<>();
        operatorCodes = new ArrayList<>();
    }
    
    private void extractIntentData() {
        Intent intent = getIntent();
        licenseKey = intent.getStringExtra("license_key");
        deviceId = intent.getStringExtra("device_id");
        
        // Fallback to preferences if not in intent
        if (licenseKey == null) licenseKey = preferences.getString("license_key", "");
        if (deviceId == null) deviceId = preferences.getString("device_id", "");
        
        Log.d(TAG, "License: " + (licenseKey != null ? licenseKey.substring(0, Math.min(8, licenseKey.length())) + "..." : "null"));
    }
    
    private void initializeUI() {
        sim1OperatorSpinner = findViewById(R.id.sim1OperatorSpinner);
        sim2OperatorSpinner = findViewById(R.id.sim2OperatorSpinner);
        autoRechargeCheckbox = findViewById(R.id.autoRechargeCheckbox);
        serverUrlField = findViewById(R.id.serverUrlField);
        apiPinField = findViewById(R.id.apiPinField);
        checkIntervalField = findViewById(R.id.checkIntervalField);
        saveSettingsButton = findViewById(R.id.saveSettingsButton);
        backButton = findViewById(R.id.backButton);
        startServiceButton = findViewById(R.id.startServiceButton);
        stopServiceButton = findViewById(R.id.stopServiceButton);
        statusText = findViewById(R.id.statusText);
        
        // Setup button listeners
        setupButtonListeners();
        
        // Update status
        updateStatus("Loading settings...");
    }
    
    private void setupButtonListeners() {
        saveSettingsButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSettings();
            }
        });
        
        backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        startServiceButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startRechargeService();
            }
        });
        
        stopServiceButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopRechargeService();
            }
        });
    }
    
    private void loadOperators() {
        // Load default operators first
        loadDefaultOperators();
        setupOperatorSpinners();
        
        // Then try to load from server
        apiClient.getOperators("BD", new RechargeApiClient.RechargeCallback() {
            @Override
            public void onResponse(JSONObject response) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (response.getBoolean("success")) {
                                // Process server operators
                                // This is a simplified implementation
                                updateStatus("Operators loaded from server");
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error processing operators response", e);
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.e(TAG, "Error loading operators: " + error);
                    }
                });
            }
        });
    }
    
    private void loadDefaultOperators() {
        operatorNames.clear();
        operatorCodes.clear();
        
        operatorNames.add("None");
        operatorNames.add("Grameenphone");
        operatorNames.add("Robi");
        operatorNames.add("Banglalink");
        operatorNames.add("Airtel");
        operatorNames.add("Teletalk");
        
        operatorCodes.add("");
        operatorCodes.add("GP");
        operatorCodes.add("ROBI");
        operatorCodes.add("BL");
        operatorCodes.add("AIRTEL");
        operatorCodes.add("TT");
    }
    
    private void setupOperatorSpinners() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, operatorNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        
        sim1OperatorSpinner.setAdapter(adapter);
        sim2OperatorSpinner.setAdapter(adapter);
    }
    
    private void loadCurrentSettings() {
        // Load from preferences
        String sim1Operator = preferences.getString("recharge_sim1_operator", "");
        String sim2Operator = preferences.getString("recharge_sim2_operator", "");
        boolean autoRecharge = preferences.getBoolean("recharge_auto_enabled", false);
        String serverUrl = preferences.getString("recharge_server_url", "");
        String apiPin = preferences.getString("recharge_api_pin", "");
        String checkInterval = preferences.getString("recharge_check_interval", "5");
        
        // Set UI values
        autoRechargeCheckbox.setChecked(autoRecharge);
        serverUrlField.setText(serverUrl);
        apiPinField.setText(apiPin);
        checkIntervalField.setText(checkInterval);
        
        updateStatus("Settings loaded");
    }
    
    private void saveSettings() {
        updateStatus("Saving settings...");
        
        // Get values from UI
        String sim1Operator = "";
        String sim2Operator = "";
        
        if (sim1OperatorSpinner.getSelectedItemPosition() > 0) {
            sim1Operator = operatorCodes.get(sim1OperatorSpinner.getSelectedItemPosition());
        }
        
        if (sim2OperatorSpinner.getSelectedItemPosition() > 0) {
            sim2Operator = operatorCodes.get(sim2OperatorSpinner.getSelectedItemPosition());
        }
        
        boolean autoRecharge = autoRechargeCheckbox.isChecked();
        String serverUrl = serverUrlField.getText().toString().trim();
        String apiPin = apiPinField.getText().toString().trim();
        String checkInterval = checkIntervalField.getText().toString().trim();
        
        // Validate
        if (checkInterval.isEmpty()) {
            checkInterval = "5";
        }
        
        try {
            int interval = Integer.parseInt(checkInterval);
            if (interval < 1 || interval > 60) {
                Toast.makeText(this, "Check interval must be between 1 and 60 seconds", Toast.LENGTH_SHORT).show();
                return;
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "Invalid check interval", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Save to preferences
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString("recharge_sim1_operator", sim1Operator);
        editor.putString("recharge_sim2_operator", sim2Operator);
        editor.putBoolean("recharge_auto_enabled", autoRecharge);
        editor.putString("recharge_server_url", serverUrl);
        editor.putString("recharge_api_pin", apiPin);
        editor.putString("recharge_check_interval", checkInterval);
        editor.apply();
        
        // Save to local database
        String settingsJson = createSettingsJson();
        dbHelper.saveRechargeSettings(licenseKey, deviceId, sim1Operator, sim2Operator, 
                                    autoRecharge, serverUrl, apiPin, settingsJson);
        
        // Save to server
        apiClient.updateRechargeSettings(sim1Operator, sim2Operator, autoRecharge, 
                                       serverUrl, apiPin, settingsJson, 
                                       new RechargeApiClient.RechargeCallback() {
            @Override
            public void onResponse(JSONObject response) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (response.getBoolean("success")) {
                                updateStatus("Settings saved successfully");
                                Toast.makeText(RechargeSettingsActivity.this, "Settings saved", Toast.LENGTH_SHORT).show();
                            } else {
                                updateStatus("Failed to save settings to server: " + response.getString("message"));
                                Toast.makeText(RechargeSettingsActivity.this, "Server save failed", Toast.LENGTH_SHORT).show();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error processing save response", e);
                            updateStatus("Error saving settings to server");
                        }
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.e(TAG, "Error saving settings: " + error);
                        updateStatus("Error saving settings: " + error);
                        Toast.makeText(RechargeSettingsActivity.this, "Save error: " + error, Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }
    
    private String createSettingsJson() {
        try {
            JSONObject settings = new JSONObject();
            settings.put("check_interval", checkIntervalField.getText().toString());
            settings.put("auto_recharge", autoRechargeCheckbox.isChecked());
            settings.put("server_url", serverUrlField.getText().toString());
            return settings.toString();
        } catch (Exception e) {
            Log.e(TAG, "Error creating settings JSON", e);
            return "{}";
        }
    }
    
    private void startRechargeService() {
        RechargeService.startRechargeService(this);
        updateStatus("Recharge service started");
        Toast.makeText(this, "Recharge service started", Toast.LENGTH_SHORT).show();
    }
    
    private void stopRechargeService() {
        RechargeService.stopRechargeService(this);
        updateStatus("Recharge service stopped");
        Toast.makeText(this, "Recharge service stopped", Toast.LENGTH_SHORT).show();
    }
    
    private void updateStatus(String status) {
        if (statusText != null) {
            statusText.setText(status);
        }
        Log.d(TAG, "Status: " + status);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (apiClient != null) {
            apiClient.cleanup();
        }
        if (dbHelper != null) {
            dbHelper.close();
        }
    }
}
