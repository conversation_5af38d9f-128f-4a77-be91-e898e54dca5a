package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * API client for recharge operations
 * Handles communication with the server for recharge requests and status updates
 */
public class RechargeApiClient {

    private static final String TAG = "RechargeApiClient";
    private static final String DEFAULT_API_URL = "http://192.168.0.106/AppyStoreMRecharge/admin/api/recharge_api.php";
    private static final int CONNECTION_TIMEOUT = 30000; // 30 seconds
    private static final int READ_TIMEOUT = 30000; // 30 seconds

    private Context context;
    private SharedPreferences preferences;
    private ExecutorService executorService;
    private String apiUrl;
    private String licenseKey;
    private String deviceId;

    public RechargeApiClient(Context context) {
        this.context = context;
        this.preferences = PreferenceManager.getDefaultSharedPreferences(context);
        this.executorService = Executors.newFixedThreadPool(3);

        // Get API configuration
        this.apiUrl = getPreference("recharge_api_url", DEFAULT_API_URL);
        this.licenseKey = getPreference("license_key", "");
        this.deviceId = getPreference("device_id", "");

        Log.d(TAG, "RechargeApiClient initialized with URL: " + apiUrl);
    }

    /**
     * Submit a recharge request to the server
     */
    public void submitRecharge(String phoneNumber, int amount, String operator, int simSlot, RechargeCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    JSONObject response = makeApiCall("submit_recharge", createRechargeParams(phoneNumber, amount, operator, simSlot));

                    if (callback != null) {
                        callback.onResponse(response);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error submitting recharge", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * Get recharge status from server
     */
    public void getRechargeStatus(String orderId, RechargeCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=get_recharge_status" +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&order_id=" + URLEncoder.encode(orderId, "UTF-8");

                    JSONObject response = makeApiCall("get_recharge_status", params);

                    if (callback != null) {
                        callback.onResponse(response);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error getting recharge status", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * Update recharge status on server
     */
    public void updateRechargeStatus(String orderId, String status, String apiResponse, String smsResponse) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=update_recharge_status" +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&order_id=" + URLEncoder.encode(orderId, "UTF-8") +
                                  "&status=" + URLEncoder.encode(status, "UTF-8");

                    if (apiResponse != null) {
                        params += "&api_response=" + URLEncoder.encode(apiResponse, "UTF-8");
                    }

                    if (smsResponse != null) {
                        params += "&sms_response=" + URLEncoder.encode(smsResponse, "UTF-8");
                    }

                    JSONObject response = makeApiCall("update_recharge_status", params);
                    Log.d(TAG, "Updated recharge status on server: " + response.toString());

                } catch (Exception e) {
                    Log.e(TAG, "Error updating recharge status", e);
                }
            }
        });
    }

    /**
     * Get available operators from server
     */
    public void getOperators(String country, RechargeCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=get_operators&country=" + URLEncoder.encode(country, "UTF-8");
                    JSONObject response = makeApiCall("get_operators", params);

                    if (callback != null) {
                        callback.onResponse(response);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error getting operators", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * Get recharge settings from server
     */
    public void getRechargeSettings(RechargeCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=get_recharge_settings" +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8");

                    JSONObject response = makeApiCall("get_recharge_settings", params);

                    if (callback != null) {
                        callback.onResponse(response);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error getting recharge settings", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * Update recharge settings on server
     */
    public void updateRechargeSettings(String sim1Operator, String sim2Operator, boolean autoRechargeEnabled,
                                     String serverUrl, String apiPin, String settingsJson, RechargeCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=update_recharge_settings" +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&sim1_operator=" + URLEncoder.encode(sim1Operator, "UTF-8") +
                                  "&sim2_operator=" + URLEncoder.encode(sim2Operator, "UTF-8") +
                                  "&auto_recharge_enabled=" + (autoRechargeEnabled ? "1" : "0") +
                                  "&server_url=" + URLEncoder.encode(serverUrl, "UTF-8") +
                                  "&api_pin=" + URLEncoder.encode(apiPin, "UTF-8") +
                                  "&settings_json=" + URLEncoder.encode(settingsJson, "UTF-8");

                    JSONObject response = makeApiCall("update_recharge_settings", params);

                    if (callback != null) {
                        callback.onResponse(response);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error updating recharge settings", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * Get recharge history from server
     */
    public void getRechargeHistory(int limit, int offset, RechargeCallback callback) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=get_recharge_history" +
                                  "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&limit=" + limit +
                                  "&offset=" + offset;

                    JSONObject response = makeApiCall("get_recharge_history", params);

                    if (callback != null) {
                        callback.onResponse(response);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error getting recharge history", e);
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                }
            }
        });
    }

    /**
     * Process SMS response
     */
    public void processSmsResponse(String sender, String messageBody, String orderId) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String params = "action=process_sms_response" +
                                  "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                  "&sender=" + URLEncoder.encode(sender, "UTF-8") +
                                  "&message_body=" + URLEncoder.encode(messageBody, "UTF-8");

                    if (orderId != null && !orderId.isEmpty()) {
                        params += "&order_id=" + URLEncoder.encode(orderId, "UTF-8");
                    }

                    JSONObject response = makeApiCall("process_sms_response", params);
                    Log.d(TAG, "Processed SMS response on server: " + response.toString());

                } catch (Exception e) {
                    Log.e(TAG, "Error processing SMS response", e);
                }
            }
        });
    }

    /**
     * Sync recharge status with server
     */
    public void syncRechargeStatus() {
        // This method can be used to periodically sync local data with server
        Log.d(TAG, "Syncing recharge status with server");
        // Implementation depends on specific sync requirements
    }

    private String createRechargeParams(String phoneNumber, int amount, String operator, int simSlot) throws Exception {
        return "action=submit_recharge" +
               "&license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
               "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
               "&phone_number=" + URLEncoder.encode(phoneNumber, "UTF-8") +
               "&amount=" + amount +
               "&operator=" + URLEncoder.encode(operator, "UTF-8") +
               "&sim_slot=" + simSlot;
    }

    private JSONObject makeApiCall(String action, String params) throws Exception {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(apiUrl);
            connection = (HttpURLConnection) url.openConnection();

            // Set request properties
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "LicenseActivate-RechargeClient/1.0");
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setDoOutput(true);

            // Send POST data
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.writeBytes(params);
                wr.flush();
            }

            // Read response
            int responseCode = connection.getResponseCode();
            Log.d(TAG, "API Response Code: " + responseCode);

            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            Log.d(TAG, "API Response: " + response.toString());

            // Parse JSON response
            return new JSONObject(response.toString());

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    private String getPreference(String key, String defaultValue) {
        return preferences.getString(key, defaultValue);
    }

    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    /**
     * Callback interface for API responses
     */
    public interface RechargeCallback {
        void onResponse(JSONObject response);
        void onError(String error);
    }
}
