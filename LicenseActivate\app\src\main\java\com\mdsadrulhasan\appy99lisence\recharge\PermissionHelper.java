package com.mdsadrulhasan.appy99lisence.recharge;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;

/**
 * Helper class for managing recharge-related permissions
 */
public class PermissionHelper {
    
    private static final String TAG = "PermissionHelper";
    
    // Required permissions for recharge functionality
    public static final String[] RECHARGE_PERMISSIONS = {
        Manifest.permission.CALL_PHONE,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.RECEIVE_SMS,
        Manifest.permission.READ_SMS
    };
    
    // Permission request codes
    public static final int REQUEST_RECHARGE_PERMISSIONS = 1001;
    
    /**
     * Check if all recharge permissions are granted
     */
    public static boolean hasAllRechargePermissions(Context context) {
        for (String permission : RECHARGE_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get list of missing permissions
     */
    public static List<String> getMissingPermissions(Context context) {
        List<String> missingPermissions = new ArrayList<>();
        
        for (String permission : RECHARGE_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }
        
        return missingPermissions;
    }
    
    /**
     * Request all missing recharge permissions
     */
    public static void requestRechargePermissions(Activity activity) {
        List<String> missingPermissions = getMissingPermissions(activity);
        
        if (!missingPermissions.isEmpty()) {
            Log.d(TAG, "Requesting " + missingPermissions.size() + " missing permissions");
            ActivityCompat.requestPermissions(activity, 
                missingPermissions.toArray(new String[0]), 
                REQUEST_RECHARGE_PERMISSIONS);
        } else {
            Log.d(TAG, "All recharge permissions already granted");
        }
    }
    
    /**
     * Check if specific permission is granted
     */
    public static boolean hasPermission(Context context, String permission) {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * Get user-friendly permission description
     */
    public static String getPermissionDescription(String permission) {
        switch (permission) {
            case Manifest.permission.CALL_PHONE:
                return "Phone calls (required for automatic USSD dialing)";
            case Manifest.permission.READ_PHONE_STATE:
                return "Phone state (required for dual-SIM detection)";
            case Manifest.permission.RECEIVE_SMS:
                return "Receive SMS (required for processing recharge responses)";
            case Manifest.permission.READ_SMS:
                return "Read SMS (required for processing recharge responses)";
            default:
                return permission;
        }
    }
    
    /**
     * Get permission status summary
     */
    public static String getPermissionStatusSummary(Context context) {
        List<String> missingPermissions = getMissingPermissions(context);
        
        if (missingPermissions.isEmpty()) {
            return "✅ All recharge permissions granted";
        } else {
            StringBuilder summary = new StringBuilder();
            summary.append("❌ Missing permissions:\n");
            
            for (String permission : missingPermissions) {
                summary.append("• ").append(getPermissionDescription(permission)).append("\n");
            }
            
            return summary.toString();
        }
    }
    
    /**
     * Check if we should show rationale for permission
     */
    public static boolean shouldShowRationale(Activity activity, String permission) {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
    }
    
    /**
     * Check if any permission was permanently denied
     */
    public static boolean hasPermissionsPermanentlyDenied(Activity activity, String[] permissions, int[] grantResults) {
        for (int i = 0; i < permissions.length; i++) {
            if (grantResults[i] == PackageManager.PERMISSION_DENIED) {
                if (!shouldShowRationale(activity, permissions[i])) {
                    return true; // Permission was permanently denied
                }
            }
        }
        return false;
    }
    
    /**
     * Get critical permissions (absolutely required for basic functionality)
     */
    public static String[] getCriticalPermissions() {
        return new String[] {
            Manifest.permission.CALL_PHONE,
            Manifest.permission.READ_PHONE_STATE
        };
    }
    
    /**
     * Check if critical permissions are granted
     */
    public static boolean hasCriticalPermissions(Context context) {
        for (String permission : getCriticalPermissions()) {
            if (!hasPermission(context, permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get optional permissions (nice to have but not critical)
     */
    public static String[] getOptionalPermissions() {
        return new String[] {
            Manifest.permission.RECEIVE_SMS,
            Manifest.permission.READ_SMS
        };
    }
}
