<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Mobile Banking Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mobile Banking Services"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <!-- bKash -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="bKash"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#E2136E" />

                    <ToggleButton
                        android:id="@+id/bkash_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>

                <!-- Rocket -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Rocket"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#8E24AA" />

                    <ToggleButton
                        android:id="@+id/rocket_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>

                <!-- Nagad -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Nagad"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#FF6600" />

                    <ToggleButton
                        android:id="@+id/nagad_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Banking Configuration -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Banking Configuration"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <!-- Auto Banking -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Auto Banking"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <ToggleButton
                        android:id="@+id/auto_banking_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </LinearLayout>

                <!-- Banking Delay -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Banking Delay (seconds)"
                        android:textSize="16sp"
                        android:textColor="#333333"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/banking_delay_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/input_background"
                        android:hint="Enter delay in seconds"
                        android:inputType="number"
                        android:padding="12dp"
                        android:text="5" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Banking Status -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Banking Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/banking_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Banking services are ready"
                    android:textSize="14sp"
                    android:textColor="#4CAF50"
                    android:layout_marginBottom="12dp" />

                <Button
                    android:id="@+id/test_banking_button"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:text="Test Banking Connection"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_background" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>
