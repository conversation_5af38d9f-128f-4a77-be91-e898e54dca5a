#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 400496 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=12720, tid=13356
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\4be887383aac33a617a0df6a13b9baa2\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\4be887383aac33a617a0df6a13b9baa2\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-076657da650823a8680ae3299281d2b6-sock

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Sun May  4 02:35:21 2025 Bangladesh Standard Time elapsed time: 22.355882 seconds (0d 0h 0m 22s)

---------------  T H R E A D  ---------------

Current thread (0x0000024be91bd7d0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=13356, stack(0x00000062e2a00000,0x00000062e2b00000) (1024K)]


Current CompileTask:
C2:22355 7840   !   4       org.eclipse.jdt.internal.compiler.util.Jdk::<init> (46 bytes)

Stack: [0x00000062e2a00000,0x00000062e2b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0xc500d]
V  [jvm.dll+0xc5543]
V  [jvm.dll+0x3b6752]
V  [jvm.dll+0x382935]
V  [jvm.dll+0x381d9a]
V  [jvm.dll+0x2479f0]
V  [jvm.dll+0x246fcf]
V  [jvm.dll+0x1c75ee]
V  [jvm.dll+0x25685a]
V  [jvm.dll+0x254dfa]
V  [jvm.dll+0x3f0256]
V  [jvm.dll+0x851f8b]
V  [jvm.dll+0x6cc5ed]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000024bf3cd6880, length=41, elements={
0x0000024b91a37ab0, 0x0000024be91acf60, 0x0000024be91b17e0, 0x0000024be91b3360,
0x0000024be91b4270, 0x0000024be91b7f40, 0x0000024be91b8990, 0x0000024be91bd7d0,
0x0000024beabb1cb0, 0x0000024beac170c0, 0x0000024beadd3990, 0x0000024bef7fe720,
0x0000024bef9830e0, 0x0000024bef7773e0, 0x0000024befa6f950, 0x0000024befe4d4c0,
0x0000024beff2d210, 0x0000024beff7c690, 0x0000024beff7cd20, 0x0000024beff7e760,
0x0000024beff7c000, 0x0000024beff7f480, 0x0000024beff7d3b0, 0x0000024beff7da40,
0x0000024beff7e0d0, 0x0000024beff7edf0, 0x0000024bf27c80a0, 0x0000024bf27c8dc0,
0x0000024bf27c8730, 0x0000024bf27c6660, 0x0000024bf27c3f00, 0x0000024bf27c4c20,
0x0000024bf27c2b50, 0x0000024bf27c4590, 0x0000024bf27c9450, 0x0000024bf27c31e0,
0x0000024bf27c7a10, 0x0000024bf27c9ae0, 0x0000024bf27c5940, 0x0000024bf27c6cf0,
0x0000024bf1c842d0
}

Java Threads: ( => current thread )
  0x0000024b91a37ab0 JavaThread "main"                              [_thread_blocked, id=5556, stack(0x00000062e2000000,0x00000062e2100000) (1024K)]
  0x0000024be91acf60 JavaThread "Reference Handler"          daemon [_thread_blocked, id=11000, stack(0x00000062e2400000,0x00000062e2500000) (1024K)]
  0x0000024be91b17e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10904, stack(0x00000062e2500000,0x00000062e2600000) (1024K)]
  0x0000024be91b3360 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=12208, stack(0x00000062e2600000,0x00000062e2700000) (1024K)]
  0x0000024be91b4270 JavaThread "Attach Listener"            daemon [_thread_blocked, id=1640, stack(0x00000062e2700000,0x00000062e2800000) (1024K)]
  0x0000024be91b7f40 JavaThread "Service Thread"             daemon [_thread_blocked, id=16256, stack(0x00000062e2800000,0x00000062e2900000) (1024K)]
  0x0000024be91b8990 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=9204, stack(0x00000062e2900000,0x00000062e2a00000) (1024K)]
=>0x0000024be91bd7d0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=13356, stack(0x00000062e2a00000,0x00000062e2b00000) (1024K)]
  0x0000024beabb1cb0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=4296, stack(0x00000062e2b00000,0x00000062e2c00000) (1024K)]
  0x0000024beac170c0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=6600, stack(0x00000062e2c00000,0x00000062e2d00000) (1024K)]
  0x0000024beadd3990 JavaThread "Notification Thread"        daemon [_thread_blocked, id=10292, stack(0x00000062e2e00000,0x00000062e2f00000) (1024K)]
  0x0000024bef7fe720 JavaThread "Active Thread: Equinox Container: ca996f24-252a-42af-9295-98c3f24bf4cc"        [_thread_blocked, id=15520, stack(0x00000062e3600000,0x00000062e3700000) (1024K)]
  0x0000024bef9830e0 JavaThread "Refresh Thread: Equinox Container: ca996f24-252a-42af-9295-98c3f24bf4cc" daemon [_thread_blocked, id=11412, stack(0x00000062e3500000,0x00000062e3600000) (1024K)]
  0x0000024bef7773e0 JavaThread "Framework Event Dispatcher: Equinox Container: ca996f24-252a-42af-9295-98c3f24bf4cc" daemon [_thread_blocked, id=7328, stack(0x00000062e3800000,0x00000062e3900000) (1024K)]
  0x0000024befa6f950 JavaThread "Start Level: Equinox Container: ca996f24-252a-42af-9295-98c3f24bf4cc" daemon [_thread_blocked, id=11696, stack(0x00000062e3900000,0x00000062e3a00000) (1024K)]
  0x0000024befe4d4c0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=7340, stack(0x00000062e3a00000,0x00000062e3b00000) (1024K)]
  0x0000024beff2d210 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=16420, stack(0x00000062e3b00000,0x00000062e3c00000) (1024K)]
  0x0000024beff7c690 JavaThread "Worker-JM"                         [_thread_blocked, id=16920, stack(0x00000062e3d00000,0x00000062e3e00000) (1024K)]
  0x0000024beff7cd20 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=396, stack(0x00000062e3c00000,0x00000062e3d00000) (1024K)]
  0x0000024beff7e760 JavaThread "Worker-0: Java indexing... "        [_thread_blocked, id=15300, stack(0x00000062e3e00000,0x00000062e3f00000) (1024K)]
  0x0000024beff7c000 JavaThread "Worker-1"                          [_thread_blocked, id=15484, stack(0x00000062e3f00000,0x00000062e4000000) (1024K)]
  0x0000024beff7f480 JavaThread "Worker-2"                          [_thread_blocked, id=16940, stack(0x00000062e4000000,0x00000062e4100000) (1024K)]
  0x0000024beff7d3b0 JavaThread "Worker-3: Initialize Workspace"        [_thread_blocked, id=14408, stack(0x00000062e4100000,0x00000062e4200000) (1024K)]
  0x0000024beff7da40 JavaThread "Java indexing"              daemon [_thread_blocked, id=6184, stack(0x00000062e4400000,0x00000062e4500000) (1024K)]
  0x0000024beff7e0d0 JavaThread "Worker-4"                          [_thread_blocked, id=12164, stack(0x00000062e4700000,0x00000062e4800000) (1024K)]
  0x0000024beff7edf0 JavaThread "Worker-5"                          [_thread_blocked, id=10964, stack(0x00000062e4800000,0x00000062e4900000) (1024K)]
  0x0000024bf27c80a0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=16536, stack(0x00000062e4900000,0x00000062e4a00000) (1024K)]
  0x0000024bf27c8dc0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=10116, stack(0x00000062e4a00000,0x00000062e4b00000) (1024K)]
  0x0000024bf27c8730 JavaThread "Thread-4"                   daemon [_thread_in_native, id=12696, stack(0x00000062e4b00000,0x00000062e4c00000) (1024K)]
  0x0000024bf27c6660 JavaThread "Thread-5"                   daemon [_thread_in_native, id=10348, stack(0x00000062e4c00000,0x00000062e4d00000) (1024K)]
  0x0000024bf27c3f00 JavaThread "Thread-6"                   daemon [_thread_in_native, id=16960, stack(0x00000062e4d00000,0x00000062e4e00000) (1024K)]
  0x0000024bf27c4c20 JavaThread "Thread-7"                   daemon [_thread_in_native, id=10716, stack(0x00000062e4e00000,0x00000062e4f00000) (1024K)]
  0x0000024bf27c2b50 JavaThread "Thread-8"                   daemon [_thread_in_native, id=11672, stack(0x00000062e4f00000,0x00000062e5000000) (1024K)]
  0x0000024bf27c4590 JavaThread "Thread-9"                   daemon [_thread_in_native, id=2408, stack(0x00000062e5000000,0x00000062e5100000) (1024K)]
  0x0000024bf27c9450 JavaThread "Thread-10"                  daemon [_thread_in_native, id=2728, stack(0x00000062e5100000,0x00000062e5200000) (1024K)]
  0x0000024bf27c31e0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=15964, stack(0x00000062e5200000,0x00000062e5300000) (1024K)]
  0x0000024bf27c7a10 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=5840, stack(0x00000062e5300000,0x00000062e5400000) (1024K)]
  0x0000024bf27c9ae0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=14976, stack(0x00000062e5400000,0x00000062e5500000) (1024K)]
  0x0000024bf27c5940 JavaThread "Timer-0"                           [_thread_blocked, id=1524, stack(0x00000062e4200000,0x00000062e4300000) (1024K)]
  0x0000024bf27c6cf0 JavaThread "Connection worker"                 [_thread_blocked, id=5972, stack(0x00000062e4300000,0x00000062e4400000) (1024K)]
  0x0000024bf1c842d0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=16632, stack(0x00000062e3400000,0x00000062e3500000) (1024K)]
Total: 41

Other Threads:
  0x0000024be91a2430 VMThread "VM Thread"                           [id=16240, stack(0x00000062e2300000,0x00000062e2400000) (1024K)]
  0x0000024b93e4ebf0 WatcherThread "VM Periodic Task Thread"        [id=7644, stack(0x00000062e2200000,0x00000062e2300000) (1024K)]
  0x0000024b93e003b0 WorkerThread "GC Thread#0"                     [id=17376, stack(0x00000062e2100000,0x00000062e2200000) (1024K)]
  0x0000024bead32770 WorkerThread "GC Thread#1"                     [id=9048, stack(0x00000062e2d00000,0x00000062e2e00000) (1024K)]
  0x0000024beacf9bf0 WorkerThread "GC Thread#2"                     [id=10660, stack(0x00000062e2f00000,0x00000062e3000000) (1024K)]
  0x0000024beacf9f90 WorkerThread "GC Thread#3"                     [id=7020, stack(0x00000062e3000000,0x00000062e3100000) (1024K)]
  0x0000024bef09b880 WorkerThread "GC Thread#4"                     [id=9648, stack(0x00000062e3100000,0x00000062e3200000) (1024K)]
  0x0000024bef09bc20 WorkerThread "GC Thread#5"                     [id=2672, stack(0x00000062e3200000,0x00000062e3300000) (1024K)]
  0x0000024beaea9f10 WorkerThread "GC Thread#6"                     [id=4308, stack(0x00000062e3300000,0x00000062e3400000) (1024K)]
  0x0000024bef7762c0 WorkerThread "GC Thread#7"                     [id=4160, stack(0x00000062e3700000,0x00000062e3800000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  22399 7840   !   4       org.eclipse.jdt.internal.compiler.util.Jdk::<init> (46 bytes)
C2 CompilerThread1  22399 7841   !   4       org.eclipse.jdt.internal.compiler.util.JRTUtil::getJrtSystem (72 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff9174ae380] Threads_lock - owner thread: 0x0000024be91a2430
[0x00007ff9174ae480] Heap_lock - owner thread: 0x0000024bf27c6cf0

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000024ba8000000-0x0000024ba8ba0000-0x0000024ba8ba0000), size 12189696, SharedBaseAddress: 0x0000024ba8000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000024ba9000000-0x0000024be9000000, reserved size: 1073741824
Narrow klass base: 0x0000024ba8000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 9216K, used 9209K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 1536K, 99% used [0x00000000eb300000,0x00000000eb47e6f0,0x00000000eb480000)
  to   space 3584K, 47% used [0x00000000eb600000,0x00000000eb7a9dc0,0x00000000eb980000)
 ParOldGen       total 168960K, used 168959K [0x00000000c0000000, 0x00000000ca500000, 0x00000000eab00000)
  object space 168960K, 99% used [0x00000000c0000000,0x00000000ca4ffec0,0x00000000ca500000)
 Metaspace       used 54050K, committed 55232K, reserved 1114112K
  class space    used 5922K, committed 6464K, reserved 1048576K

Card table byte_map: [0x0000024b93790000,0x0000024b939a0000] _byte_map_base: 0x0000024b93190000

Marking Bits: (ParMarkBitMap*) 0x00007ff9174b3260
 Begin Bits: [0x0000024ba5fe0000, 0x0000024ba6fe0000)
 End Bits:   [0x0000024ba6fe0000, 0x0000024ba7fe0000)

Polling page: 0x0000024b91dc0000

Metaspace:

Usage:
  Non-class:     47.00 MB used.
      Class:      5.78 MB used.
       Both:     52.78 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      47.62 MB ( 74%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      53.94 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  15.59 MB
       Class:  9.51 MB
        Both:  25.10 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 892.
num_arena_deaths: 16.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 863.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 3250.
num_chunk_merges: 12.
num_chunk_splits: 2084.
num_chunks_enlarged: 1295.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=5674Kb max_used=5674Kb free=114325Kb
 bounds [0x0000024b9eab0000, 0x0000024b9f040000, 0x0000024ba5fe0000]
CodeHeap 'profiled nmethods': size=120000Kb used=15430Kb max_used=15430Kb free=104569Kb
 bounds [0x0000024b96fe0000, 0x0000024b97f00000, 0x0000024b9e510000]
CodeHeap 'non-nmethods': size=5760Kb used=1397Kb max_used=1465Kb free=4362Kb
 bounds [0x0000024b9e510000, 0x0000024b9e780000, 0x0000024b9eab0000]
 total_blobs=7963 nmethods=7258 adapters=611
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 22.311 Thread 0x0000024bf1c842d0 7848       4       java.io.File::equals (28 bytes)
Event: 22.314 Thread 0x0000024be91bd7d0 nmethod 7820 0x0000024b9f031210 code [0x0000024b9f031a40, 0x0000024b9f0369a0]
Event: 22.316 Thread 0x0000024be91bd7d0 7840   !   4       org.eclipse.jdt.internal.compiler.util.Jdk::<init> (46 bytes)
Event: 22.320 Thread 0x0000024beabb1cb0 nmethod 7853 0x0000024b97ee0b90 code [0x0000024b97ee1580, 0x0000024b97ee7170]
Event: 22.320 Thread 0x0000024beabb1cb0 7854       3       org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList::get (57 bytes)
Event: 22.320 Thread 0x0000024bf1c842d0 nmethod 7848 0x0000024b9f039f10 code [0x0000024b9f03a0c0, 0x0000024b9f03a5b0]
Event: 22.321 Thread 0x0000024bf1c842d0 7841   !   4       org.eclipse.jdt.internal.compiler.util.JRTUtil::getJrtSystem (72 bytes)
Event: 22.321 Thread 0x0000024beabb1cb0 nmethod 7854 0x0000024b97eead90 code [0x0000024b97eeb000, 0x0000024b97eeb7e0]
Event: 22.324 Thread 0x0000024beabb1cb0 7855       3       java.net.URI$Parser::parse (243 bytes)
Event: 22.327 Thread 0x0000024beabb1cb0 nmethod 7855 0x0000024b97eebb90 code [0x0000024b97eec080, 0x0000024b97eee2b0]
Event: 22.327 Thread 0x0000024beabb1cb0 7856       3       java.lang.invoke.LambdaFormEditor::buffer (12 bytes)
Event: 22.327 Thread 0x0000024beabb1cb0 nmethod 7856 0x0000024b97eef090 code [0x0000024b97eef240, 0x0000024b97eef3f8]
Event: 22.327 Thread 0x0000024beabb1cb0 7857       3       java.lang.invoke.InvokerBytecodeGenerator::methodPrologue (29 bytes)
Event: 22.327 Thread 0x0000024beabb1cb0 nmethod 7857 0x0000024b97eef490 code [0x0000024b97eef660, 0x0000024b97eef908]
Event: 22.327 Thread 0x0000024beabb1cb0 7858       3       java.lang.invoke.InvokerBytecodeGenerator::emitReturn (107 bytes)
Event: 22.328 Thread 0x0000024beabb1cb0 nmethod 7858 0x0000024b97eefa90 code [0x0000024b97eefca0, 0x0000024b97ef0158]
Event: 22.344 Thread 0x0000024beabb1cb0 7860       3       java.util.regex.Pattern::curly (66 bytes)
Event: 22.344 Thread 0x0000024beabb1cb0 nmethod 7860 0x0000024b97ef0390 code [0x0000024b97ef05e0, 0x0000024b97ef11b8]
Event: 22.347 Thread 0x0000024beabb1cb0 7861       3       sun.net.www.protocol.jar.JarURLConnection::getUseCaches (8 bytes)
Event: 22.347 Thread 0x0000024beabb1cb0 nmethod 7861 0x0000024b97ef1510 code [0x0000024b97ef16c0, 0x0000024b97ef1898]

GC Heap History (20 events):
Event: 21.012 GC heap before
{Heap before GC invocations=72 (full 2):
 PSYoungGen      total 9728K, used 9409K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 84% used [0x00000000eb280000,0x00000000eb4304b0,0x00000000eb480000)
  to   space 2560K, 0% used [0x00000000eb500000,0x00000000eb500000,0x00000000eb780000)
 ParOldGen       total 153600K, used 153548K [0x00000000c0000000, 0x00000000c9600000, 0x00000000eab00000)
  object space 153600K, 99% used [0x00000000c0000000,0x00000000c95f3108,0x00000000c9600000)
 Metaspace       used 52921K, committed 54144K, reserved 1114112K
  class space    used 5710K, committed 6272K, reserved 1048576K
}
Event: 21.031 GC heap after
{Heap after GC invocations=72 (full 2):
 PSYoungGen      total 9728K, used 1977K [0x00000000eab00000, 0x00000000eb700000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 2048K, 96% used [0x00000000eb500000,0x00000000eb6ee4e0,0x00000000eb700000)
  to   space 2048K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb480000)
 ParOldGen       total 155648K, used 155171K [0x00000000c0000000, 0x00000000c9800000, 0x00000000eab00000)
  object space 155648K, 99% used [0x00000000c0000000,0x00000000c9788f60,0x00000000c9800000)
 Metaspace       used 52921K, committed 54144K, reserved 1114112K
  class space    used 5710K, committed 6272K, reserved 1048576K
}
Event: 21.083 GC heap before
{Heap before GC invocations=73 (full 2):
 PSYoungGen      total 9728K, used 9657K [0x00000000eab00000, 0x00000000eb700000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 96% used [0x00000000eb500000,0x00000000eb6ee4e0,0x00000000eb700000)
  to   space 2048K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb480000)
 ParOldGen       total 155648K, used 155171K [0x00000000c0000000, 0x00000000c9800000, 0x00000000eab00000)
  object space 155648K, 99% used [0x00000000c0000000,0x00000000c9788f60,0x00000000c9800000)
 Metaspace       used 52921K, committed 54144K, reserved 1114112K
  class space    used 5710K, committed 6272K, reserved 1048576K
}
Event: 21.102 GC heap after
{Heap after GC invocations=73 (full 2):
 PSYoungGen      total 9728K, used 1874K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 2048K, 91% used [0x00000000eb280000,0x00000000eb454b68,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 157184K, used 156991K [0x00000000c0000000, 0x00000000c9980000, 0x00000000eab00000)
  object space 157184K, 99% used [0x00000000c0000000,0x00000000c994ffa8,0x00000000c9980000)
 Metaspace       used 52921K, committed 54144K, reserved 1114112K
  class space    used 5710K, committed 6272K, reserved 1048576K
}
Event: 21.404 GC heap before
{Heap before GC invocations=74 (full 2):
 PSYoungGen      total 9728K, used 9554K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 91% used [0x00000000eb280000,0x00000000eb454b68,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 157184K, used 156991K [0x00000000c0000000, 0x00000000c9980000, 0x00000000eab00000)
  object space 157184K, 99% used [0x00000000c0000000,0x00000000c994ffa8,0x00000000c9980000)
 Metaspace       used 52959K, committed 54144K, reserved 1114112K
  class space    used 5714K, committed 6272K, reserved 1048576K
}
Event: 21.413 GC heap after
{Heap after GC invocations=74 (full 2):
 PSYoungGen      total 9728K, used 1859K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 2048K, 90% used [0x00000000eb480000,0x00000000eb650f38,0x00000000eb680000)
  to   space 2048K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb480000)
 ParOldGen       total 159232K, used 158763K [0x00000000c0000000, 0x00000000c9b80000, 0x00000000eab00000)
  object space 159232K, 99% used [0x00000000c0000000,0x00000000c9b0adb8,0x00000000c9b80000)
 Metaspace       used 52959K, committed 54144K, reserved 1114112K
  class space    used 5714K, committed 6272K, reserved 1048576K
}
Event: 21.616 GC heap before
{Heap before GC invocations=75 (full 2):
 PSYoungGen      total 9728K, used 9539K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 90% used [0x00000000eb480000,0x00000000eb650f38,0x00000000eb680000)
  to   space 2048K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb480000)
 ParOldGen       total 159232K, used 158763K [0x00000000c0000000, 0x00000000c9b80000, 0x00000000eab00000)
  object space 159232K, 99% used [0x00000000c0000000,0x00000000c9b0adb8,0x00000000c9b80000)
 Metaspace       used 53081K, committed 54272K, reserved 1114112K
  class space    used 5743K, committed 6272K, reserved 1048576K
}
Event: 21.628 GC heap after
{Heap after GC invocations=75 (full 2):
 PSYoungGen      total 9728K, used 1668K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 2048K, 81% used [0x00000000eb280000,0x00000000eb4213e8,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 160768K, used 160487K [0x00000000c0000000, 0x00000000c9d00000, 0x00000000eab00000)
  object space 160768K, 99% used [0x00000000c0000000,0x00000000c9cb9f90,0x00000000c9d00000)
 Metaspace       used 53081K, committed 54272K, reserved 1114112K
  class space    used 5743K, committed 6272K, reserved 1048576K
}
Event: 21.999 GC heap before
{Heap before GC invocations=76 (full 2):
 PSYoungGen      total 9728K, used 9348K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 81% used [0x00000000eb280000,0x00000000eb4213e8,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 160768K, used 160487K [0x00000000c0000000, 0x00000000c9d00000, 0x00000000eab00000)
  object space 160768K, 99% used [0x00000000c0000000,0x00000000c9cb9f90,0x00000000c9d00000)
 Metaspace       used 53542K, committed 54720K, reserved 1114112K
  class space    used 5826K, committed 6400K, reserved 1048576K
}
Event: 22.003 GC heap after
{Heap after GC invocations=76 (full 2):
 PSYoungGen      total 8704K, used 911K [0x00000000eab00000, 0x00000000eb580000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 1024K, 89% used [0x00000000eb480000,0x00000000eb563f48,0x00000000eb580000)
  to   space 1536K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb400000)
 ParOldGen       total 162304K, used 162179K [0x00000000c0000000, 0x00000000c9e80000, 0x00000000eab00000)
  object space 162304K, 99% used [0x00000000c0000000,0x00000000c9e60e78,0x00000000c9e80000)
 Metaspace       used 53542K, committed 54720K, reserved 1114112K
  class space    used 5826K, committed 6400K, reserved 1048576K
}
Event: 22.053 GC heap before
{Heap before GC invocations=77 (full 2):
 PSYoungGen      total 8704K, used 8591K [0x00000000eab00000, 0x00000000eb580000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 1024K, 89% used [0x00000000eb480000,0x00000000eb563f48,0x00000000eb580000)
  to   space 1536K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb400000)
 ParOldGen       total 162304K, used 162179K [0x00000000c0000000, 0x00000000c9e80000, 0x00000000eab00000)
  object space 162304K, 99% used [0x00000000c0000000,0x00000000c9e60e78,0x00000000c9e80000)
 Metaspace       used 53686K, committed 54848K, reserved 1114112K
  class space    used 5859K, committed 6400K, reserved 1048576K
}
Event: 22.061 GC heap after
{Heap after GC invocations=77 (full 2):
 PSYoungGen      total 9216K, used 1481K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 1536K, 96% used [0x00000000eb280000,0x00000000eb3f2488,0x00000000eb400000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 163328K, used 162988K [0x00000000c0000000, 0x00000000c9f80000, 0x00000000eab00000)
  object space 163328K, 99% used [0x00000000c0000000,0x00000000c9f2b260,0x00000000c9f80000)
 Metaspace       used 53686K, committed 54848K, reserved 1114112K
  class space    used 5859K, committed 6400K, reserved 1048576K
}
Event: 22.172 GC heap before
{Heap before GC invocations=78 (full 2):
 PSYoungGen      total 9216K, used 9161K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 1536K, 96% used [0x00000000eb280000,0x00000000eb3f2488,0x00000000eb400000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 163328K, used 162988K [0x00000000c0000000, 0x00000000c9f80000, 0x00000000eab00000)
  object space 163328K, 99% used [0x00000000c0000000,0x00000000c9f2b260,0x00000000c9f80000)
 Metaspace       used 53769K, committed 54976K, reserved 1114112K
  class space    used 5875K, committed 6464K, reserved 1048576K
}
Event: 22.175 GC heap after
{Heap after GC invocations=78 (full 2):
 PSYoungGen      total 9728K, used 1715K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 2048K, 83% used [0x00000000eb480000,0x00000000eb62cff0,0x00000000eb680000)
  to   space 2048K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb480000)
 ParOldGen       total 164352K, used 164345K [0x00000000c0000000, 0x00000000ca080000, 0x00000000eab00000)
  object space 164352K, 99% used [0x00000000c0000000,0x00000000ca07e538,0x00000000ca080000)
 Metaspace       used 53769K, committed 54976K, reserved 1114112K
  class space    used 5875K, committed 6464K, reserved 1048576K
}
Event: 22.220 GC heap before
{Heap before GC invocations=79 (full 2):
 PSYoungGen      total 9728K, used 9395K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 83% used [0x00000000eb480000,0x00000000eb62cff0,0x00000000eb680000)
  to   space 2048K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb480000)
 ParOldGen       total 164352K, used 164345K [0x00000000c0000000, 0x00000000ca080000, 0x00000000eab00000)
  object space 164352K, 99% used [0x00000000c0000000,0x00000000ca07e538,0x00000000ca080000)
 Metaspace       used 53863K, committed 55040K, reserved 1114112K
  class space    used 5896K, committed 6464K, reserved 1048576K
}
Event: 22.221 GC heap after
{Heap after GC invocations=79 (full 2):
 PSYoungGen      total 9728K, used 1612K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 2048K, 78% used [0x00000000eb280000,0x00000000eb4131f8,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 166400K, used 165924K [0x00000000c0000000, 0x00000000ca280000, 0x00000000eab00000)
  object space 166400K, 99% used [0x00000000c0000000,0x00000000ca2092b0,0x00000000ca280000)
 Metaspace       used 53863K, committed 55040K, reserved 1114112K
  class space    used 5896K, committed 6464K, reserved 1048576K
}
Event: 22.280 GC heap before
{Heap before GC invocations=80 (full 2):
 PSYoungGen      total 9728K, used 9292K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 2048K, 78% used [0x00000000eb280000,0x00000000eb4131f8,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb680000)
 ParOldGen       total 166400K, used 165924K [0x00000000c0000000, 0x00000000ca280000, 0x00000000eab00000)
  object space 166400K, 99% used [0x00000000c0000000,0x00000000ca2092b0,0x00000000ca280000)
 Metaspace       used 53866K, committed 55040K, reserved 1114112K
  class space    used 5896K, committed 6464K, reserved 1048576K
}
Event: 22.283 GC heap after
{Heap after GC invocations=80 (full 2):
 PSYoungGen      total 9216K, used 1478K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 1536K, 96% used [0x00000000eb480000,0x00000000eb5f1a20,0x00000000eb600000)
  to   space 1536K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb480000)
 ParOldGen       total 167424K, used 167354K [0x00000000c0000000, 0x00000000ca380000, 0x00000000eab00000)
  object space 167424K, 99% used [0x00000000c0000000,0x00000000ca36ea68,0x00000000ca380000)
 Metaspace       used 53866K, committed 55040K, reserved 1114112K
  class space    used 5896K, committed 6464K, reserved 1048576K
}
Event: 22.318 GC heap before
{Heap before GC invocations=81 (full 2):
 PSYoungGen      total 9216K, used 9158K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 7680K, 100% used [0x00000000eab00000,0x00000000eb280000,0x00000000eb280000)
  from space 1536K, 96% used [0x00000000eb480000,0x00000000eb5f1a20,0x00000000eb600000)
  to   space 1536K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb480000)
 ParOldGen       total 167424K, used 167354K [0x00000000c0000000, 0x00000000ca380000, 0x00000000eab00000)
  object space 167424K, 99% used [0x00000000c0000000,0x00000000ca36ea68,0x00000000ca380000)
 Metaspace       used 53867K, committed 55040K, reserved 1114112K
  class space    used 5896K, committed 6464K, reserved 1048576K
}
Event: 22.320 GC heap after
{Heap after GC invocations=81 (full 2):
 PSYoungGen      total 9216K, used 1529K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 7680K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb280000)
  from space 1536K, 99% used [0x00000000eb300000,0x00000000eb47e6f0,0x00000000eb480000)
  to   space 3584K, 0% used [0x00000000eb600000,0x00000000eb600000,0x00000000eb980000)
 ParOldGen       total 168960K, used 168823K [0x00000000c0000000, 0x00000000ca500000, 0x00000000eab00000)
  object space 168960K, 99% used [0x00000000c0000000,0x00000000ca4ddec0,0x00000000ca500000)
 Metaspace       used 53867K, committed 55040K, reserved 1114112K
  class space    used 5896K, committed 6464K, reserved 1048576K
}

Dll operation events (11 events):
Event: 0.039 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.290 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.343 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.349 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.353 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.357 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.395 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.542 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 2.475 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 5.077 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-109405\jna13805244111518507309.dll
Event: 21.682 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll

Deoptimization events (20 events):
Event: 22.209 Thread 0x0000024bf27c6cf0 DEOPT PACKING pc=0x0000024b9ebc0ff0 sp=0x00000062e43fcf00
Event: 22.209 Thread 0x0000024bf27c6cf0 DEOPT UNPACKING pc=0x0000024b9e563aa2 sp=0x00000062e43fcd98 mode 2
Event: 22.217 Thread 0x0000024bf27c6cf0 DEOPT PACKING pc=0x0000024b97bd834e sp=0x00000062e43fc640
Event: 22.217 Thread 0x0000024bf27c6cf0 DEOPT UNPACKING pc=0x0000024b9e564242 sp=0x00000062e43fbbc0 mode 0
Event: 22.232 Thread 0x0000024bf27c6cf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000024b9ef7dd34 relative=0x00000000000004f4
Event: 22.232 Thread 0x0000024bf27c6cf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000024b9ef7dd34 method=java.net.URI$Parser.scan(IILjava/lang/String;Ljava/lang/String;)I @ 6 c2
Event: 22.232 Thread 0x0000024bf27c6cf0 DEOPT PACKING pc=0x0000024b9ef7dd34 sp=0x00000062e43fce80
Event: 22.232 Thread 0x0000024bf27c6cf0 DEOPT UNPACKING pc=0x0000024b9e563aa2 sp=0x00000062e43fcde0 mode 2
Event: 22.250 Thread 0x0000024bf27c6cf0 DEOPT PACKING pc=0x0000024b97bd834e sp=0x00000062e43fc640
Event: 22.250 Thread 0x0000024bf27c6cf0 DEOPT UNPACKING pc=0x0000024b9e564242 sp=0x00000062e43fbbc0 mode 0
Event: 22.289 Thread 0x0000024bf27c6cf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000024b9ebb844c relative=0x000000000000150c
Event: 22.289 Thread 0x0000024bf27c6cf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000024b9ebb844c method=java.lang.String.<init>(Ljava/nio/charset/Charset;[BII)V @ 113 c2
Event: 22.289 Thread 0x0000024bf27c6cf0 DEOPT PACKING pc=0x0000024b9ebb844c sp=0x00000062e43fc9c0
Event: 22.289 Thread 0x0000024bf27c6cf0 DEOPT UNPACKING pc=0x0000024b9e563aa2 sp=0x00000062e43fc990 mode 2
Event: 22.294 Thread 0x0000024bf27c6cf0 DEOPT PACKING pc=0x0000024b97bd834e sp=0x00000062e43fc640
Event: 22.294 Thread 0x0000024bf27c6cf0 DEOPT UNPACKING pc=0x0000024b9e564242 sp=0x00000062e43fbbc0 mode 0
Event: 22.309 Thread 0x0000024beff7da40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000024b9efa1750 relative=0x0000000000006bd0
Event: 22.309 Thread 0x0000024beff7da40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000024b9efa1750 method=org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer.indexDocument()V @ 55 c2
Event: 22.309 Thread 0x0000024beff7da40 DEOPT PACKING pc=0x0000024b9efa1750 sp=0x00000062e44fed20
Event: 22.309 Thread 0x0000024beff7da40 DEOPT UNPACKING pc=0x0000024b9e563aa2 sp=0x00000062e44fec80 mode 2

Classes loaded (20 events):
Event: 19.591 Loading class java/nio/file/FileChannelLinesSpliterator
Event: 19.592 Loading class java/nio/file/FileChannelLinesSpliterator done
Event: 19.593 Loading class java/util/stream/Streams$1
Event: 19.593 Loading class java/util/stream/Streams$1 done
Event: 19.594 Loading class java/nio/file/FileChannelLinesSpliterator$1
Event: 19.594 Loading class java/nio/file/FileChannelLinesSpliterator$1 done
Event: 19.693 Loading class java/util/concurrent/CopyOnWriteArraySet
Event: 19.693 Loading class java/util/concurrent/CopyOnWriteArraySet done
Event: 19.745 Loading class java/util/concurrent/ArrayBlockingQueue
Event: 19.747 Loading class java/util/concurrent/ArrayBlockingQueue done
Event: 19.837 Loading class java/net/HttpURLConnection
Event: 19.837 Loading class java/net/HttpURLConnection done
Event: 19.847 Loading class java/io/FileFilter
Event: 19.848 Loading class java/io/FileFilter done
Event: 19.873 Loading class java/util/concurrent/ConcurrentHashMap$ValueSpliterator
Event: 19.874 Loading class java/util/concurrent/ConcurrentHashMap$ValueSpliterator done
Event: 19.882 Loading class jdk/internal/loader/BootLoader$PackageHelper
Event: 19.891 Loading class jdk/internal/loader/BootLoader$PackageHelper done
Event: 21.823 Loading class java/lang/Class$EnclosingMethodInfo
Event: 21.824 Loading class java/lang/Class$EnclosingMethodInfo done

Classes unloaded (8 events):
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9199400 'java/lang/invoke/LambdaForm$MH+0x0000024ba9199400'
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9199000 'java/lang/invoke/LambdaForm$MH+0x0000024ba9199000'
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9198c00 'java/lang/invoke/LambdaForm$MH+0x0000024ba9198c00'
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9198800 'java/lang/invoke/LambdaForm$MH+0x0000024ba9198800'
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9198400 'java/lang/invoke/LambdaForm$BMH+0x0000024ba9198400'
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9198000 'java/lang/invoke/LambdaForm$DMH+0x0000024ba9198000'
Event: 6.512 Thread 0x0000024be91a2430 Unloading class 0x0000024ba9196c00 'java/lang/invoke/LambdaForm$DMH+0x0000024ba9196c00'
Event: 9.384 Thread 0x0000024be91a2430 Unloading class 0x0000024ba92aa400 'java/lang/invoke/LambdaForm$MH+0x0000024ba92aa400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 14.252 Thread 0x0000024beff7c000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead2f8b8}> (0x00000000ead2f8b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.255 Thread 0x0000024beff7c000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead39000}> (0x00000000ead39000) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.255 Thread 0x0000024beff7c000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead398e0}> (0x00000000ead398e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.306 Thread 0x0000024beff7c000 Exception <a 'java/io/FileNotFoundException'{0x00000000eb047650}> (0x00000000eb047650) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.306 Thread 0x0000024beff7c000 Exception <a 'java/io/FileNotFoundException'{0x00000000eb048638}> (0x00000000eb048638) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.307 Thread 0x0000024beff7c000 Exception <a 'java/io/FileNotFoundException'{0x00000000eb049db0}> (0x00000000eb049db0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.307 Thread 0x0000024beff7c000 Exception <a 'java/io/FileNotFoundException'{0x00000000eb04b060}> (0x00000000eb04b060) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.308 Thread 0x0000024beff7c000 Exception <a 'java/io/FileNotFoundException'{0x00000000eb04c068}> (0x00000000eb04c068) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.841 Thread 0x0000024beff7c000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab66cf0}> (0x00000000eab66cf0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.842 Thread 0x0000024beff7c000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab67320}> (0x00000000eab67320) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 15.408 Thread 0x0000024beff7da40 Implicit null exception at 0x0000024b9ee3abda to 0x0000024b9ee3acd8
Event: 16.990 Thread 0x0000024beff7da40 Implicit null exception at 0x0000024b9ef0d677 to 0x0000024b9ef16b18
Event: 17.802 Thread 0x0000024beff7da40 Implicit null exception at 0x0000024b9ef4e4fc to 0x0000024b9ef53d14
Event: 19.420 Thread 0x0000024beff7d3b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead8ea80}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ead8ea80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.530 Thread 0x0000024beff7d3b0 Implicit null exception at 0x0000024b9ef2f72a to 0x0000024b9ef307d8
Event: 19.553 Thread 0x0000024beff7d3b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb22a210}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000eb22a210) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.553 Thread 0x0000024beff7d3b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eb250a38}: Found class java.lang.Object, but interface was expected> (0x00000000eb250a38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 19.580 Thread 0x0000024beff7d3b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf66330}: method resolution failed> (0x00000000eaf66330) 
thrown [s\src\hotspot\share\prims\methodHandles.cpp, line 1144]
Event: 19.695 Thread 0x0000024beff7d3b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead4b918}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ead4b918) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.699 Thread 0x0000024beff7d3b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead6e288}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ead6e288) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 20.999 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 21.031 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.078 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 21.102 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.404 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 21.413 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.616 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 21.628 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 21.866 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.003 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.053 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.061 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.172 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.175 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.220 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.221 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.280 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.283 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 22.318 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.320 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97671d10
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97672610
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97673810
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97674010
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97675d10
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97676610
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97676e90
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97678390
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97678e10
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97679790
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976a3a10
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976ab590
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976ac790
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976b1510
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976c7610
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976c8c90
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b976e3c90
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97714c90
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b97718990
Event: 9.397 Thread 0x0000024be91a2430 flushing  nmethod 0x0000024b9778d990

Events (20 events):
Event: 9.779 Thread 0x0000024b91a37ab0 Thread added: 0x0000024bf27c9450
Event: 9.823 Thread 0x0000024b91a37ab0 Thread added: 0x0000024bf27c31e0
Event: 10.246 Thread 0x0000024b91a37ab0 Thread added: 0x0000024bf27c7a10
Event: 10.247 Thread 0x0000024b91a37ab0 Thread added: 0x0000024bf27c9ae0
Event: 11.094 Thread 0x0000024bf1cbf520 Thread exited: 0x0000024bf1cbf520
Event: 11.094 Thread 0x0000024bf02b3290 Thread exited: 0x0000024bf02b3290
Event: 11.574 Thread 0x0000024bef264b40 Thread exited: 0x0000024bef264b40
Event: 12.552 Thread 0x0000024beabb1cb0 Thread added: 0x0000024beff600f0
Event: 13.283 Thread 0x0000024beabb1cb0 Thread added: 0x0000024bf3c10a10
Event: 14.681 Thread 0x0000024bf3c10a10 Thread exited: 0x0000024bf3c10a10
Event: 14.890 Thread 0x0000024beabb1cb0 Thread added: 0x0000024bf1c842d0
Event: 18.979 Thread 0x0000024bf1c842d0 Thread exited: 0x0000024bf1c842d0
Event: 18.979 Thread 0x0000024beff600f0 Thread exited: 0x0000024beff600f0
Event: 19.114 Thread 0x0000024beabb1cb0 Thread added: 0x0000024bf1c842d0
Event: 19.643 Thread 0x0000024beff7d3b0 Thread added: 0x0000024bf27c5940
Event: 19.775 Thread 0x0000024beff7d3b0 Thread added: 0x0000024bf27c6cf0
Event: 20.560 Thread 0x0000024bf1c842d0 Thread exited: 0x0000024bf1c842d0
Event: 21.136 Thread 0x0000024beabb1cb0 Thread added: 0x0000024bf1c842d0
Event: 21.833 Thread 0x0000024bf1c842d0 Thread exited: 0x0000024bf1c842d0
Event: 22.204 Thread 0x0000024beabb1cb0 Thread added: 0x0000024bf1c842d0


Dynamic libraries:
0x00007ff6c4e60000 - 0x00007ff6c4e6e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ff999a10000 - 0x00007ff999c08000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff997b90000 - 0x00007ff997c52000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff997510000 - 0x00007ff997806000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff997910000 - 0x00007ff997a10000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff9761d0000 - 0x00007ff9761e8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ff997cd0000 - 0x00007ff997e6d000 	C:\WINDOWS\System32\USER32.dll
0x00007ff997a10000 - 0x00007ff997a32000 	C:\WINDOWS\System32\win32u.dll
0x00007ff9982e0000 - 0x00007ff99830b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff997350000 - 0x00007ff99746a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff997470000 - 0x00007ff99750d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff96f250000 - 0x00007ff96f26e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff9866c0000 - 0x00007ff98695a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ff9986e0000 - 0x00007ff99877e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff998c10000 - 0x00007ff998c3f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff98f190000 - 0x00007ff98f19c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff9686b0000 - 0x00007ff96873d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ff916800000 - 0x00007ff917590000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ff997a40000 - 0x00007ff997aef000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff998640000 - 0x00007ff9986df000 	C:\WINDOWS\System32\sechost.dll
0x00007ff998330000 - 0x00007ff998453000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff9970b0000 - 0x00007ff9970d7000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff997c60000 - 0x00007ff997ccb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff996f10000 - 0x00007ff996f5b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff98f170000 - 0x00007ff98f17a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff98bed0000 - 0x00007ff98bef7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff996ef0000 - 0x00007ff996f02000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff995920000 - 0x00007ff995932000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff98d7f0000 - 0x00007ff98d7fa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ff994f20000 - 0x00007ff995104000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff986030000 - 0x00007ff986064000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff9970e0000 - 0x00007ff997162000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff976300000 - 0x00007ff97630f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ff96eb80000 - 0x00007ff96eb9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ff999260000 - 0x00007ff9999ce000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff995110000 - 0x00007ff9958b3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff998de0000 - 0x00007ff999133000 	C:\WINDOWS\System32\combase.dll
0x00007ff996a10000 - 0x00007ff996a3b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ff997e70000 - 0x00007ff997f3d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff997f40000 - 0x00007ff997fed000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff9981a0000 - 0x00007ff9981f5000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff996fe0000 - 0x00007ff997005000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff969aa0000 - 0x00007ff969ab8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ff9899c0000 - 0x00007ff9899d0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ff9936b0000 - 0x00007ff9937ba000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff996770000 - 0x00007ff9967dc000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff969940000 - 0x00007ff969956000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ff976170000 - 0x00007ff976180000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ff965310000 - 0x00007ff965355000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff998460000 - 0x00007ff99858b000 	C:\WINDOWS\System32\ole32.dll
0x00007ff996960000 - 0x00007ff996978000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff996090000 - 0x00007ff9960c8000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff996f60000 - 0x00007ff996f8e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff996980000 - 0x00007ff99698c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff996450000 - 0x00007ff99648b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff998310000 - 0x00007ff998318000 	C:\WINDOWS\System32\NSI.dll
0x00007ff962f00000 - 0x00007ff962f49000 	C:\Users\<USER>\AppData\Local\Temp\jna-109405\jna13805244111518507309.dll
0x00007ff997af0000 - 0x00007ff997af8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff9914e0000 - 0x00007ff9914f7000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff991490000 - 0x00007ff9914ad000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff98d8e0000 - 0x00007ff98d907000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-109405;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\4be887383aac33a617a0df6a13b9baa2\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\4be887383aac33a617a0df6a13b9baa2\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-076657da650823a8680ae3299281d2b6-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\4be887383aac33a617a0df6a13b9baa2\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 4:38 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4662M free)
TotalPageFile size 22476M (AvailPageFile size 16M)
current process WorkingSet (physical memory assigned to process): 416M, peak: 416M
current process commit charge ("private bytes"): 406M, peak: 924M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
