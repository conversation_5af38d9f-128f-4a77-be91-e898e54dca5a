<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="@drawable/rounded_cornerss"
    android:layout_marginBottom="4dp">

    <TextView
        android:id="@+id/log_time_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="12:34:56"
        android:textSize="12sp"
        android:textColor="#666666"
        android:layout_marginEnd="8dp"
        android:minWidth="60dp" />

    <TextView
        android:id="@+id/log_message_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Log message"
        android:textSize="14sp"
        android:textColor="#333333"
        android:layout_marginEnd="8dp" />

    <TextView
        android:id="@+id/log_status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Status"
        android:textSize="12sp"
        android:textStyle="bold"
        android:textColor="#4CAF50"
        android:minWidth="60dp"
        android:gravity="end" />

</LinearLayout>
