package com.appystore.mrecharge;


import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class WResponse {
    @SerializedName("auto")
    @Expose
    private Integer auto;
    @SerializedName("balance")
    @Expose
    private String balance;
    @SerializedName("1st")
    @Expose
    private String first;
    @SerializedName("5th")
    @Expose
    private String five;
    @SerializedName("4rd")
    @Expose
    private String four;
    @SerializedName("id")
    @Expose
    private String id;
    @SerializedName("line")
    @Expose
    private Integer line;
    @SerializedName("number")
    @Expose
    private String number;
    @SerializedName("pcode")
    @Expose
    private String pcode;
    @SerializedName("powerload")
    @Expose
    private Integer powerload;
    @SerializedName("resend")
    @Expose
    private Integer resend;
    @SerializedName("2nd")
    @Expose
    private String second;
    @SerializedName("slot")
    @Expose
    private Integer slot;
    @SerializedName("sms")
    @Expose
    private Integer sms;
    @SerializedName("smstext")
    @Expose
    private String smstext;
    @SerializedName("status")
    @Expose
    private Integer status;
    @SerializedName("3rd")
    @Expose
    private String three;
    @SerializedName("title")
    @Expose
    private String title;
    @SerializedName("triger")
    @Expose
    private Integer triger;
    @SerializedName("ussd")
    @Expose
    private String ussd;

    public Integer getpowerload() {
        return this.powerload;
    }

    public Integer getResend() {
        return this.resend;
    }

    public Integer getstatus() {
        return this.status;
    }

    public void setstatus(Integer num) {
        this.status = num;
    }

    public String getnumber() {
        return this.number;
    }

    public String getbalance() {
        return this.balance;
    }

    public String getpcode() {
        return this.pcode;
    }

    public String gettitle() {
        return this.title;
    }

    public String getid() {
        return this.id;
    }

    public String getussd() {
        return this.ussd;
    }

    public String getsmstext() {
        return this.smstext;
    }

    public String getfirst() {
        return this.first;
    }

    public String getsecond() {
        return this.second;
    }

    public String getthree() {
        return this.three;
    }

    public String getfour() {
        return this.four;
    }

    public String getfive() {
        return this.five;
    }

    public Integer getslot() {
        return this.slot;
    }

    public Integer getauto() {
        return this.auto;
    }

    public Integer getsms() {
        return this.sms;
    }

    public Integer gettriger() {
        return this.triger;
    }

    public Integer getline() {
        return this.line;
    }
}
