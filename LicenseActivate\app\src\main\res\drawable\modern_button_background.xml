<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#5E35B1"
                android:endColor="#7E57C2"
                android:angle="45" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Disabled state -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#BDBDBD" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#673AB7"
                android:endColor="#9C27B0"
                android:angle="45" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
</selector>
