<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/telecom_dashboard"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:keepScreenOn="true"
    android:orientation="vertical">

    <!-- Top Navigation Bar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/navigation_container"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#3F51B5"
        android:elevation="8dp">

        <!-- Home Tab -->
        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="#4A5DC7"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/nav_banking"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintHorizontal_chainStyle="spread">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home_black_24dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Home"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Banking Tab -->
        <LinearLayout
            android:id="@+id/nav_banking"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@null"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@id/nav_home"
            app:layout_constraintEnd_toStartOf="@id/nav_settings"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings_cell_black_24dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Banking"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- Settings Tab -->
        <LinearLayout
            android:id="@+id/nav_settings"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@null"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@id/nav_banking"
            app:layout_constraintEnd_toStartOf="@id/nav_monitor"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings_black_24dp"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Settings"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- Monitor Tab -->
        <LinearLayout
            android:id="@+id/nav_monitor"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@null"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@id/nav_settings"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_baseline_add_to_queue_24"
                app:tint="#FFFFFF" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Monitor"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- App Logo and Title -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/app_logo"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginTop="16dp"
                    app:srcCompat="@mipmap/ic_launcher" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp"
                    android:text="Automatic Mobile Recharge System"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#FFFFFF"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/device_id_display"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="8dp"
                    android:text="Device ID: Loading..."
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#F42121"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- Service Status Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rounded_corner"
                android:elevation="4dp"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Service Status"
                    android:textColor="#3F51B5"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/service_status_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Service Status: Checking..."
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <Button
                        android:id="@+id/start_service_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/button_background"
                        android:text="Start Service"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />

                    <Button
                        android:id="@+id/stop_service_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/button_background"
                        android:text="Stop Service"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Network Settings -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rounded_corner"
                android:elevation="4dp"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Network Settings"
                    android:textColor="#3F51B5"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="SIM A:"
                        android:textColor="#3F51B5"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/sim_a_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@android:color/white"
                        android:padding="8dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="SIM B:"
                        android:textColor="#3F51B5"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/sim_b_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@android:color/white"
                        android:padding="8dp" />
                </LinearLayout>
            </LinearLayout>

            <!-- System Settings -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rounded_corner"
                android:elevation="4dp"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="System Settings"
                    android:textColor="#3F51B5"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- SMS Service -->
                <RelativeLayout
                    android:id="@+id/sms_service_setting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="SMS Service"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff090909" />

                    <ToggleButton
                        android:id="@+id/sms_service_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <!-- Accessibility -->
                <RelativeLayout
                    android:id="@+id/accessibility_setting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="Accessibility"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/accessibility_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>

                <!-- Power Optimize -->
                <RelativeLayout
                    android:id="@+id/power_optimize_setting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rounded_cornerss"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="Power Optimize"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="#ff080908" />

                    <ToggleButton
                        android:id="@+id/power_optimize_toggle"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/modern_toggle_selector"
                        android:checked="false"
                        android:text=""
                        android:textOff=""
                        android:textOn="" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
