package com.mdsadrulhasan.appy99lisence.telecom;

import android.accessibilityservice.AccessibilityService;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;

/**
 * Accessibility service for handling USSD responses
 * Integrated from backup/app project into LicenseActivate
 */
public class TelecomUSSDService extends AccessibilityService {
    
    private static final String TAG = "TelecomUSSDService";
    
    private TelecomDbHelper dbHelper;
    private TelecomDialFunction dialFunction;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "TelecomUSSDService created");
        
        // Initialize database helper
        dbHelper = new TelecomDbHelper(getApplicationContext());
        
        // Initialize dial function
        dialFunction = new TelecomDialFunction(getApplicationContext());
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "TelecomUSSDService destroyed");
        
        // Clean up resources
        if (dbHelper != null) {
            dbHelper.close();
        }
        
        if (dialFunction != null) {
            dialFunction.cleanup();
        }
    }
    
    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        try {
            if (event == null) {
                return;
            }
            
            Log.d(TAG, "onAccessibilityEvent: " + event.getEventType());
            
            // Only process window content changed and window state changed events
            if (event.getEventType() != AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED &&
                event.getEventType() != AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
                return;
            }
            
            // Get the source node
            AccessibilityNodeInfo source = event.getSource();
            if (source == null) {
                return;
            }
            
            // Check if this is a USSD dialog
            if (isUssdDialog(source)) {
                Log.d(TAG, "USSD dialog detected");
                handleUssdDialog(source);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onAccessibilityEvent", e);
        }
    }
    
    @Override
    public void onInterrupt() {
        Log.d(TAG, "TelecomUSSDService interrupted");
    }
    
    /**
     * Check if the current dialog is a USSD dialog
     */
    private boolean isUssdDialog(AccessibilityNodeInfo source) {
        try {
            String packageName = source.getPackageName() != null ? source.getPackageName().toString() : "";
            
            // Common USSD dialog package names
            if (packageName.contains("com.android.phone") ||
                packageName.contains("com.android.dialer") ||
                packageName.contains("ussd")) {
                
                // Look for text content that indicates USSD response
                String dialogText = extractTextFromNode(source);
                if (dialogText != null && (
                    dialogText.toLowerCase().contains("ussd") ||
                    dialogText.toLowerCase().contains("balance") ||
                    dialogText.toLowerCase().contains("recharge") ||
                    dialogText.toLowerCase().contains("tk") ||
                    dialogText.toLowerCase().contains("taka") ||
                    dialogText.toLowerCase().contains("successful") ||
                    dialogText.toLowerCase().contains("failed"))) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking USSD dialog", e);
            return false;
        }
    }
    
    /**
     * Handle USSD dialog response
     */
    private void handleUssdDialog(AccessibilityNodeInfo source) {
        try {
            // Extract text from the dialog
            String ussdResponse = extractTextFromNode(source);
            
            if (ussdResponse != null && !ussdResponse.trim().isEmpty()) {
                Log.d(TAG, "USSD Response: " + ussdResponse);
                
                // Process the USSD response
                processUssdResponse(ussdResponse);
                
                // Try to dismiss the dialog
                dismissUssdDialog(source);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling USSD dialog", e);
        }
    }
    
    /**
     * Process USSD response and update database
     */
    private void processUssdResponse(String response) {
        try {
            // Get current order ID from preferences
            String orderId = getPreference("main_id");
            
            if (orderId != null && !orderId.equals("0") && !orderId.equals("t")) {
                Log.d(TAG, "Processing USSD response for order: " + orderId);
                
                // Determine if recharge was successful
                boolean isSuccessful = isRechargeSuccessful(response);
                String status = isSuccessful ? "completed" : "failed";
                
                // Update database
                if (dbHelper != null) {
                    dbHelper.updateRechargeStatus(orderId, status, response);
                }
                
                // Send response to server
                if (dialFunction != null) {
                    dialFunction.sendResponse(orderId, response, status);
                }
                
                // Clear the order ID
                savePreference("main_id", "0");
                
                // Clear busy flag
                SharedPreferences.Editor edit = getSharedPreferences("telecom_service", 0).edit();
                edit.putInt("busy", 0);
                edit.apply();
                
                Log.d(TAG, "USSD response processed successfully");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing USSD response", e);
        }
    }
    
    /**
     * Extract text from accessibility node
     */
    private String extractTextFromNode(AccessibilityNodeInfo node) {
        if (node == null) {
            return null;
        }
        
        StringBuilder text = new StringBuilder();
        
        // Get text from current node
        if (node.getText() != null) {
            text.append(node.getText().toString()).append(" ");
        }
        
        // Get text from child nodes
        for (int i = 0; i < node.getChildCount(); i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child != null) {
                String childText = extractTextFromNode(child);
                if (childText != null) {
                    text.append(childText).append(" ");
                }
                child.recycle();
            }
        }
        
        return text.toString().trim();
    }
    
    /**
     * Dismiss USSD dialog
     */
    private void dismissUssdDialog(AccessibilityNodeInfo source) {
        try {
            // Look for dismiss buttons
            AccessibilityNodeInfo dismissButton = findNodeByText(source, "OK");
            if (dismissButton == null) {
                dismissButton = findNodeByText(source, "Cancel");
            }
            if (dismissButton == null) {
                dismissButton = findNodeByText(source, "Close");
            }
            
            if (dismissButton != null && dismissButton.isClickable()) {
                dismissButton.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                Log.d(TAG, "USSD dialog dismissed");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error dismissing USSD dialog", e);
        }
    }
    
    /**
     * Find node by text content
     */
    private AccessibilityNodeInfo findNodeByText(AccessibilityNodeInfo node, String text) {
        if (node == null || text == null) {
            return null;
        }
        
        // Check current node
        if (node.getText() != null && node.getText().toString().equalsIgnoreCase(text)) {
            return node;
        }
        
        // Check child nodes
        for (int i = 0; i < node.getChildCount(); i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child != null) {
                AccessibilityNodeInfo result = findNodeByText(child, text);
                if (result != null) {
                    return result;
                }
                child.recycle();
            }
        }
        
        return null;
    }
    
    /**
     * Determine if recharge was successful based on response text
     */
    private boolean isRechargeSuccessful(String response) {
        if (response == null) {
            return false;
        }
        
        String lowerResponse = response.toLowerCase();
        
        // Success indicators
        if (lowerResponse.contains("successful") ||
            lowerResponse.contains("success") ||
            lowerResponse.contains("completed") ||
            lowerResponse.contains("recharged") ||
            lowerResponse.contains("credited")) {
            return true;
        }
        
        // Failure indicators
        if (lowerResponse.contains("failed") ||
            lowerResponse.contains("error") ||
            lowerResponse.contains("insufficient") ||
            lowerResponse.contains("invalid")) {
            return false;
        }
        
        // Default to false if unclear
        return false;
    }
    
    /**
     * Save preference
     */
    private void savePreference(String key, String value) {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(this).edit();
        editor.putString(key, value);
        editor.apply();
    }
    
    /**
     * Get preference
     */
    private String getPreference(String key) {
        return PreferenceManager.getDefaultSharedPreferences(this).getString(key, null);
    }
}
