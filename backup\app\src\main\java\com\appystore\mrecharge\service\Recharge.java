package com.appystore.mrecharge.service;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.preference.PreferenceManager;
import android.util.Log;
import android.widget.Toast;

import androidx.core.app.NotificationCompat;

import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.Dialfunction;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.activity.MainActivity;

public class Recharge extends Service {
    private static final String TAG = "RechargeService";
    private static final String CHANNEL_ID = "RechargeServiceChannel";
    private static final int NOTIFICATION_ID = 4;

    private Handler handler;
    private Runnable runnable;
    private int runTime = 5000; // Default 5 seconds
    private SharedPreferences settings;
    private DbHelper dbHelper;
    private Dialfunction dialFunction;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate");

        // Initialize preferences
        settings = getSharedPreferences("serv", 0);

        // Initialize helpers
        dbHelper = new DbHelper(getApplicationContext());
        dialFunction = new Dialfunction(getApplicationContext());

        // Get runtime from preferences (in seconds, convert to milliseconds)
        runTime = Integer.parseInt(getPreference("stime", "5")) * 1000;

        // Initialize handler for periodic tasks
        handler = new Handler();
        setupPeriodicTask();
    }

    private void setupPeriodicTask() {
        runnable = new Runnable() {
            @Override
            public void run() {
                if (settings.getInt("stop", 0) == 1) {
                    // Process recharge requests
                    processRechargeRequests();
                }

                // Schedule next run
                handler.postDelayed(this, runTime);
            }
        };
    }

    private void processRechargeRequests() {
        // Check if service is busy
        if (settings.getInt("busy", 0) != 0) {
            Log.d(TAG, "Service is busy, skipping recharge processing");
            return;
        }

        // Process pending recharge requests
        Cursor cursor = dbHelper.fetchPendingRecharges();
        if (cursor != null && cursor.moveToFirst()) {
            try {
                String orderId = cursor.getString(cursor.getColumnIndex("orderid"));
                String ussdCode = cursor.getString(cursor.getColumnIndex("ussd"));
                String slotStr = cursor.getString(cursor.getColumnIndex("slot"));
                String id = cursor.getString(cursor.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID));

                int slot = 0;
                try {
                    slot = Integer.parseInt(slotStr);
                } catch (NumberFormatException e) {
                    Log.e(TAG, "Invalid slot number", e);
                }

                // Set busy flag
                SharedPreferences.Editor editor = settings.edit();
                editor.putInt("busy", 1);
                editor.apply();

                // Save main ID for reference
                savePreference("main_id", id);

                // Dial USSD code
                dialFunction.dialUp(ussdCode, slot, orderId);
                Log.d(TAG, "Dialing USSD code: " + ussdCode + " on slot: " + slot);

                // Show toast
                Toast.makeText(getApplicationContext(),
                        "Processing recharge: " + ussdCode,
                        Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                Log.e(TAG, "Error processing recharge request", e);
            } finally {
                cursor.close();
            }
        } else {
            Log.d(TAG, "No pending recharge requests found");
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand");

        if (intent == null) {
            return START_STICKY;
        }

        String action = intent.getStringExtra("serv");
        if (action != null && action.equals("off")) {
            stopService();
            return START_NOT_STICKY;
        }

        String notificationText = intent.getStringExtra("inputExtra");
        if (notificationText == null) {
            notificationText = "Recharge Service is running";
        }

        startForegroundService(notificationText);

        // Start periodic task
        handler.postDelayed(runnable, runTime);

        return START_STICKY;
    }

    private void startForegroundService(String contentText) {
        createNotificationChannel();

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, 0);
        }

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Recharge Service Running")
                .setContentText(contentText)
                .setSmallIcon(R.drawable.ic_stat_name)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);

        startForeground(NOTIFICATION_ID, builder.build());
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Recharge Service Channel",
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    private void stopService() {
        Log.d(TAG, "Stopping service");
        handler.removeCallbacks(runnable);
        stopForeground(true);
        stopSelf();
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "Service onDestroy");
        handler.removeCallbacks(runnable);
        super.onDestroy();
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    // Get preference with default value
    private String getPreference(String key, String defaultValue) {
        return PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).getString(key, defaultValue);
    }

    // Save preference
    private void savePreference(String key, String value) {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        editor.putString(key, value);
        editor.apply();
    }
}
