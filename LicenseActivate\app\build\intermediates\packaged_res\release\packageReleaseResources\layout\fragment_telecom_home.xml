<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Statistics Cards -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="2"
        android:layout_marginBottom="16dp">

        <!-- Total Recharges Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Total Recharges"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/total_recharges_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#3F51B5" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Success Rate Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:background="@drawable/card_background"
            android:elevation="4dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Success Rate"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/success_rate_percentage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#4CAF50" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>

    <!-- Recent Activity -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/card_background"
        android:elevation="4dp"
        android:radius="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Recent Activity"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#3F51B5"
                android:layout_marginBottom="16dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recent_activity_recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/no_activity_text"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="No recent activity"
                android:textSize="16sp"
                android:textColor="#666666"
                android:gravity="center"
                android:visibility="gone" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>
