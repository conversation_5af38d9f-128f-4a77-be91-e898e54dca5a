#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 950096 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=3796, tid=20088
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx1024m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 29 05:21:17 2025 Bangladesh Standard Time elapsed time: 7.202791 seconds (0d 0h 0m 7s)

---------------  T H R E A D  ---------------

Current thread (0x0000023c1a59a0a0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=20088, stack(0x000000a54e700000,0x000000a54e800000) (1024K)]


Current CompileTask:
C2:   7202 3022   !   4       java.net.URL::<init> (543 bytes)

Stack: [0x000000a54e700000,0x000000a54e800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0xc3f3d]
V  [jvm.dll+0xc4473]
V  [jvm.dll+0x3b5bf2]
V  [jvm.dll+0x382855]
V  [jvm.dll+0x381cca]
V  [jvm.dll+0x249bd0]
V  [jvm.dll+0x2491b1]
V  [jvm.dll+0x1c9634]
V  [jvm.dll+0x258859]
V  [jvm.dll+0x256e3a]
V  [jvm.dll+0x3ef6c6]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000023c62b03630, length=26, elements={
0x0000023c7edd4d80, 0x0000023c1a57ddc0, 0x0000023c1a57ece0, 0x0000023c1a582e80,
0x0000023c1a5848f0, 0x0000023c1a585350, 0x0000023c1a58d6d0, 0x0000023c1a59a0a0,
0x0000023c1a59a750, 0x0000023c1a722f10, 0x0000023c1a720e40, 0x0000023c1a721b60,
0x0000023c1a722880, 0x0000023c1a720120, 0x0000023c1a7214d0, 0x0000023c1a7221f0,
0x0000023c61a0aa10, 0x0000023c61a0f8d0, 0x0000023c61a0cae0, 0x0000023c61a0d170,
0x0000023c61a0b730, 0x0000023c61a08fd0, 0x0000023c61a0d800, 0x0000023c61a0b0a0,
0x0000023c61a0c450, 0x0000023c63a3a520
}

Java Threads: ( => current thread )
  0x0000023c7edd4d80 JavaThread "main"                              [_thread_blocked, id=292, stack(0x000000a54d900000,0x000000a54da00000) (1024K)]
  0x0000023c1a57ddc0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=22516, stack(0x000000a54e100000,0x000000a54e200000) (1024K)]
  0x0000023c1a57ece0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=2852, stack(0x000000a54e200000,0x000000a54e300000) (1024K)]
  0x0000023c1a582e80 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16032, stack(0x000000a54e300000,0x000000a54e400000) (1024K)]
  0x0000023c1a5848f0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19820, stack(0x000000a54e400000,0x000000a54e500000) (1024K)]
  0x0000023c1a585350 JavaThread "Service Thread"             daemon [_thread_blocked, id=21280, stack(0x000000a54e500000,0x000000a54e600000) (1024K)]
  0x0000023c1a58d6d0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=20336, stack(0x000000a54e600000,0x000000a54e700000) (1024K)]
=>0x0000023c1a59a0a0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=20088, stack(0x000000a54e700000,0x000000a54e800000) (1024K)]
  0x0000023c1a59a750 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=23380, stack(0x000000a54e800000,0x000000a54e900000) (1024K)]
  0x0000023c1a722f10 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=18092, stack(0x000000a54e900000,0x000000a54ea00000) (1024K)]
  0x0000023c1a720e40 JavaThread "Notification Thread"        daemon [_thread_blocked, id=13664, stack(0x000000a54ea00000,0x000000a54eb00000) (1024K)]
  0x0000023c1a721b60 JavaThread "Daemon health stats"               [_thread_blocked, id=22148, stack(0x000000a54eb00000,0x000000a54ec00000) (1024K)]
  0x0000023c1a722880 JavaThread "Incoming local TCP Connector on port 57707"        [_thread_in_native, id=8060, stack(0x000000a54ec00000,0x000000a54ed00000) (1024K)]
  0x0000023c1a720120 JavaThread "Daemon periodic checks"            [_thread_blocked, id=3636, stack(0x000000a54f500000,0x000000a54f600000) (1024K)]
  0x0000023c1a7214d0 JavaThread "Daemon"                            [_thread_blocked, id=19736, stack(0x000000a54f600000,0x000000a54f700000) (1024K)]
  0x0000023c1a7221f0 JavaThread "Handler for socket connection from /127.0.0.1:57707 to /127.0.0.1:57708"        [_thread_in_native, id=15608, stack(0x000000a54f700000,0x000000a54f800000) (1024K)]
  0x0000023c61a0aa10 JavaThread "Cancel handler"                    [_thread_blocked, id=6884, stack(0x000000a54f800000,0x000000a54f900000) (1024K)]
  0x0000023c61a0f8d0 JavaThread "Daemon worker"                     [_thread_in_Java, id=8708, stack(0x000000a54f900000,0x000000a54fa00000) (1024K)]
  0x0000023c61a0cae0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:57707 to /127.0.0.1:57708"        [_thread_blocked, id=4908, stack(0x000000a54fa00000,0x000000a54fb00000) (1024K)]
  0x0000023c61a0d170 JavaThread "Stdin handler"                     [_thread_blocked, id=1692, stack(0x000000a54fb00000,0x000000a54fc00000) (1024K)]
  0x0000023c61a0b730 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=18404, stack(0x000000a54fc00000,0x000000a54fd00000) (1024K)]
  0x0000023c61a08fd0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=22488, stack(0x000000a54f400000,0x000000a54f500000) (1024K)]
  0x0000023c61a0d800 JavaThread "File lock request listener"        [_thread_in_native, id=17296, stack(0x000000a54fd00000,0x000000a54fe00000) (1024K)]
  0x0000023c61a0b0a0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)"        [_thread_blocked, id=8084, stack(0x000000a54ff00000,0x000000a550000000) (1024K)]
  0x0000023c61a0c450 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileContent)"        [_thread_blocked, id=15596, stack(0x000000a54fe00000,0x000000a54ff00000) (1024K)]
  0x0000023c63a3a520 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=23192, stack(0x000000a550000000,0x000000a550100000) (1024K)]
Total: 26

Other Threads:
  0x0000023c1a563a40 VMThread "VM Thread"                           [id=11944, stack(0x000000a54e000000,0x000000a54e100000) (1024K)]
  0x0000023c1a6212c0 WatcherThread "VM Periodic Task Thread"        [id=2892, stack(0x000000a54df00000,0x000000a54e000000) (1024K)]
  0x0000023c7ee10a50 WorkerThread "GC Thread#0"                     [id=17544, stack(0x000000a54da00000,0x000000a54db00000) (1024K)]
  0x0000023c60195680 WorkerThread "GC Thread#1"                     [id=23200, stack(0x000000a54ed00000,0x000000a54ee00000) (1024K)]
  0x0000023c60195a20 WorkerThread "GC Thread#2"                     [id=6740, stack(0x000000a54ee00000,0x000000a54ef00000) (1024K)]
  0x0000023c60195dc0 WorkerThread "GC Thread#3"                     [id=19980, stack(0x000000a54ef00000,0x000000a54f000000) (1024K)]
  0x0000023c60196160 WorkerThread "GC Thread#4"                     [id=17308, stack(0x000000a54f000000,0x000000a54f100000) (1024K)]
  0x0000023c60196500 WorkerThread "GC Thread#5"                     [id=11556, stack(0x000000a54f100000,0x000000a54f200000) (1024K)]
  0x0000023c6022e9d0 WorkerThread "GC Thread#6"                     [id=16988, stack(0x000000a54f200000,0x000000a54f300000) (1024K)]
  0x0000023c6022ed70 WorkerThread "GC Thread#7"                     [id=10584, stack(0x000000a54f300000,0x000000a54f400000) (1024K)]
  0x0000023c7ee19a30 ConcurrentGCThread "G1 Main Marker"            [id=17228, stack(0x000000a54db00000,0x000000a54dc00000) (1024K)]
  0x0000023c7ee1a6e0 WorkerThread "G1 Conc#0"                       [id=21600, stack(0x000000a54dc00000,0x000000a54dd00000) (1024K)]
  0x0000023c61a82670 WorkerThread "G1 Conc#1"                       [id=14476, stack(0x000000a550100000,0x000000a550200000) (1024K)]
  0x0000023c7ee9e970 ConcurrentGCThread "G1 Refine#0"               [id=20056, stack(0x000000a54dd00000,0x000000a54de00000) (1024K)]
  0x0000023c1a4a27e0 ConcurrentGCThread "G1 Service"                [id=10024, stack(0x000000a54de00000,0x000000a54df00000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0     7228 3022   !   4       java.net.URL::<init> (543 bytes)
C2 CompilerThread1     7228 3086       4       java.util.ArrayList::grow (60 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000023c1b000000-0x0000023c1bc90000-0x0000023c1bc90000), size 13172736, SharedBaseAddress: 0x0000023c1b000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000023c1c000000-0x0000023c5c000000, reserved size: 1073741824
Narrow klass base: 0x0000023c1b000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 96256K, used 26853K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 13 survivors (13312K)
 Metaspace       used 20845K, committed 21568K, reserved 1114112K
  class space    used 3014K, committed 3328K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000c0000000, 0x00000000c0100000, 0x00000000c0100000|100%| O|  |TAMS 0x00000000c0000000| PB 0x00000000c0000000| Untracked 
|   1|0x00000000c0100000, 0x00000000c0200000, 0x00000000c0200000|100%|HS|  |TAMS 0x00000000c0100000| PB 0x00000000c0100000| Complete 
|   2|0x00000000c0200000, 0x00000000c0300000, 0x00000000c0300000|100%|HC|  |TAMS 0x00000000c0200000| PB 0x00000000c0200000| Complete 
|   3|0x00000000c0300000, 0x00000000c0400000, 0x00000000c0400000|100%|HC|  |TAMS 0x00000000c0300000| PB 0x00000000c0300000| Complete 
|   4|0x00000000c0400000, 0x00000000c0500000, 0x00000000c0500000|100%| O|  |TAMS 0x00000000c0400000| PB 0x00000000c0400000| Untracked 
|   5|0x00000000c0500000, 0x00000000c0600000, 0x00000000c0600000|100%| O|  |TAMS 0x00000000c0500000| PB 0x00000000c0500000| Untracked 
|   6|0x00000000c0600000, 0x00000000c0700000, 0x00000000c0700000|100%| O|  |TAMS 0x00000000c0600000| PB 0x00000000c0600000| Untracked 
|   7|0x00000000c0700000, 0x00000000c0800000, 0x00000000c0800000|100%|HS|  |TAMS 0x00000000c0700000| PB 0x00000000c0700000| Complete 
|   8|0x00000000c0800000, 0x00000000c0800000, 0x00000000c0900000|  0%| F|  |TAMS 0x00000000c0800000| PB 0x00000000c0800000| Untracked 
|   9|0x00000000c0900000, 0x00000000c0a00000, 0x00000000c0a00000|100%| O|  |TAMS 0x00000000c0900000| PB 0x00000000c0900000| Untracked 
|  10|0x00000000c0a00000, 0x00000000c0b00000, 0x00000000c0b00000|100%| O|  |TAMS 0x00000000c0a00000| PB 0x00000000c0a00000| Untracked 
|  11|0x00000000c0b00000, 0x00000000c0c00000, 0x00000000c0c00000|100%| O|  |TAMS 0x00000000c0b00000| PB 0x00000000c0b00000| Untracked 
|  12|0x00000000c0c00000, 0x00000000c0d00000, 0x00000000c0d00000|100%| O|  |TAMS 0x00000000c0c00000| PB 0x00000000c0c00000| Untracked 
|  13|0x00000000c0d00000, 0x00000000c0d05e50, 0x00000000c0e00000|  2%| O|Cm|TAMS 0x00000000c0d00000| PB 0x00000000c0d00000| Complete 
|  14|0x00000000c0e00000, 0x00000000c0e00000, 0x00000000c0f00000|  0%| F|  |TAMS 0x00000000c0e00000| PB 0x00000000c0e00000| Untracked 
|  15|0x00000000c0f00000, 0x00000000c0f00000, 0x00000000c1000000|  0%| F|  |TAMS 0x00000000c0f00000| PB 0x00000000c0f00000| Untracked 
|  16|0x00000000c1000000, 0x00000000c1000000, 0x00000000c1100000|  0%| F|  |TAMS 0x00000000c1000000| PB 0x00000000c1000000| Untracked 
|  17|0x00000000c1100000, 0x00000000c1100000, 0x00000000c1200000|  0%| F|  |TAMS 0x00000000c1100000| PB 0x00000000c1100000| Untracked 
|  18|0x00000000c1200000, 0x00000000c1200000, 0x00000000c1300000|  0%| F|  |TAMS 0x00000000c1200000| PB 0x00000000c1200000| Untracked 
|  19|0x00000000c1300000, 0x00000000c1300000, 0x00000000c1400000|  0%| F|  |TAMS 0x00000000c1300000| PB 0x00000000c1300000| Untracked 
|  20|0x00000000c1400000, 0x00000000c1400000, 0x00000000c1500000|  0%| F|  |TAMS 0x00000000c1400000| PB 0x00000000c1400000| Untracked 
|  21|0x00000000c1500000, 0x00000000c1500000, 0x00000000c1600000|  0%| F|  |TAMS 0x00000000c1500000| PB 0x00000000c1500000| Untracked 
|  22|0x00000000c1600000, 0x00000000c1600000, 0x00000000c1700000|  0%| F|  |TAMS 0x00000000c1600000| PB 0x00000000c1600000| Untracked 
|  23|0x00000000c1700000, 0x00000000c1700000, 0x00000000c1800000|  0%| F|  |TAMS 0x00000000c1700000| PB 0x00000000c1700000| Untracked 
|  24|0x00000000c1800000, 0x00000000c1800000, 0x00000000c1900000|  0%| F|  |TAMS 0x00000000c1800000| PB 0x00000000c1800000| Untracked 
|  25|0x00000000c1900000, 0x00000000c1900000, 0x00000000c1a00000|  0%| F|  |TAMS 0x00000000c1900000| PB 0x00000000c1900000| Untracked 
|  26|0x00000000c1a00000, 0x00000000c1a00000, 0x00000000c1b00000|  0%| F|  |TAMS 0x00000000c1a00000| PB 0x00000000c1a00000| Untracked 
|  27|0x00000000c1b00000, 0x00000000c1b00000, 0x00000000c1c00000|  0%| F|  |TAMS 0x00000000c1b00000| PB 0x00000000c1b00000| Untracked 
|  28|0x00000000c1c00000, 0x00000000c1c00000, 0x00000000c1d00000|  0%| F|  |TAMS 0x00000000c1c00000| PB 0x00000000c1c00000| Untracked 
|  29|0x00000000c1d00000, 0x00000000c1d00000, 0x00000000c1e00000|  0%| F|  |TAMS 0x00000000c1d00000| PB 0x00000000c1d00000| Untracked 
|  30|0x00000000c1e00000, 0x00000000c1e00000, 0x00000000c1f00000|  0%| F|  |TAMS 0x00000000c1e00000| PB 0x00000000c1e00000| Untracked 
|  31|0x00000000c1f00000, 0x00000000c1f00000, 0x00000000c2000000|  0%| F|  |TAMS 0x00000000c1f00000| PB 0x00000000c1f00000| Untracked 
|  32|0x00000000c2000000, 0x00000000c2000000, 0x00000000c2100000|  0%| F|  |TAMS 0x00000000c2000000| PB 0x00000000c2000000| Untracked 
|  33|0x00000000c2100000, 0x00000000c2100000, 0x00000000c2200000|  0%| F|  |TAMS 0x00000000c2100000| PB 0x00000000c2100000| Untracked 
|  34|0x00000000c2200000, 0x00000000c2200000, 0x00000000c2300000|  0%| F|  |TAMS 0x00000000c2200000| PB 0x00000000c2200000| Untracked 
|  35|0x00000000c2300000, 0x00000000c2300000, 0x00000000c2400000|  0%| F|  |TAMS 0x00000000c2300000| PB 0x00000000c2300000| Untracked 
|  36|0x00000000c2400000, 0x00000000c2400000, 0x00000000c2500000|  0%| F|  |TAMS 0x00000000c2400000| PB 0x00000000c2400000| Untracked 
|  37|0x00000000c2500000, 0x00000000c2500000, 0x00000000c2600000|  0%| F|  |TAMS 0x00000000c2500000| PB 0x00000000c2500000| Untracked 
|  38|0x00000000c2600000, 0x00000000c2600000, 0x00000000c2700000|  0%| F|  |TAMS 0x00000000c2600000| PB 0x00000000c2600000| Untracked 
|  39|0x00000000c2700000, 0x00000000c2700000, 0x00000000c2800000|  0%| F|  |TAMS 0x00000000c2700000| PB 0x00000000c2700000| Untracked 
|  40|0x00000000c2800000, 0x00000000c2800000, 0x00000000c2900000|  0%| F|  |TAMS 0x00000000c2800000| PB 0x00000000c2800000| Untracked 
|  41|0x00000000c2900000, 0x00000000c2900000, 0x00000000c2a00000|  0%| F|  |TAMS 0x00000000c2900000| PB 0x00000000c2900000| Untracked 
|  42|0x00000000c2a00000, 0x00000000c2a00000, 0x00000000c2b00000|  0%| F|  |TAMS 0x00000000c2a00000| PB 0x00000000c2a00000| Untracked 
|  43|0x00000000c2b00000, 0x00000000c2b00000, 0x00000000c2c00000|  0%| F|  |TAMS 0x00000000c2b00000| PB 0x00000000c2b00000| Untracked 
|  44|0x00000000c2c00000, 0x00000000c2c00000, 0x00000000c2d00000|  0%| F|  |TAMS 0x00000000c2c00000| PB 0x00000000c2c00000| Untracked 
|  45|0x00000000c2d00000, 0x00000000c2d00000, 0x00000000c2e00000|  0%| F|  |TAMS 0x00000000c2d00000| PB 0x00000000c2d00000| Untracked 
|  46|0x00000000c2e00000, 0x00000000c2e00000, 0x00000000c2f00000|  0%| F|  |TAMS 0x00000000c2e00000| PB 0x00000000c2e00000| Untracked 
|  47|0x00000000c2f00000, 0x00000000c2f00000, 0x00000000c3000000|  0%| F|  |TAMS 0x00000000c2f00000| PB 0x00000000c2f00000| Untracked 
|  48|0x00000000c3000000, 0x00000000c3000000, 0x00000000c3100000|  0%| F|  |TAMS 0x00000000c3000000| PB 0x00000000c3000000| Untracked 
|  49|0x00000000c3100000, 0x00000000c3100000, 0x00000000c3200000|  0%| F|  |TAMS 0x00000000c3100000| PB 0x00000000c3100000| Untracked 
|  50|0x00000000c3200000, 0x00000000c3200000, 0x00000000c3300000|  0%| F|  |TAMS 0x00000000c3200000| PB 0x00000000c3200000| Untracked 
|  51|0x00000000c3300000, 0x00000000c3300000, 0x00000000c3400000|  0%| F|  |TAMS 0x00000000c3300000| PB 0x00000000c3300000| Untracked 
|  52|0x00000000c3400000, 0x00000000c3400000, 0x00000000c3500000|  0%| F|  |TAMS 0x00000000c3400000| PB 0x00000000c3400000| Untracked 
|  53|0x00000000c3500000, 0x00000000c3500000, 0x00000000c3600000|  0%| F|  |TAMS 0x00000000c3500000| PB 0x00000000c3500000| Untracked 
|  54|0x00000000c3600000, 0x00000000c3600000, 0x00000000c3700000|  0%| F|  |TAMS 0x00000000c3600000| PB 0x00000000c3600000| Untracked 
|  55|0x00000000c3700000, 0x00000000c3700000, 0x00000000c3800000|  0%| F|  |TAMS 0x00000000c3700000| PB 0x00000000c3700000| Untracked 
|  56|0x00000000c3800000, 0x00000000c3800000, 0x00000000c3900000|  0%| F|  |TAMS 0x00000000c3800000| PB 0x00000000c3800000| Untracked 
|  57|0x00000000c3900000, 0x00000000c3900000, 0x00000000c3a00000|  0%| F|  |TAMS 0x00000000c3900000| PB 0x00000000c3900000| Untracked 
|  58|0x00000000c3a00000, 0x00000000c3a00000, 0x00000000c3b00000|  0%| F|  |TAMS 0x00000000c3a00000| PB 0x00000000c3a00000| Untracked 
|  59|0x00000000c3b00000, 0x00000000c3b00000, 0x00000000c3c00000|  0%| F|  |TAMS 0x00000000c3b00000| PB 0x00000000c3b00000| Untracked 
|  60|0x00000000c3c00000, 0x00000000c3c00000, 0x00000000c3d00000|  0%| F|  |TAMS 0x00000000c3c00000| PB 0x00000000c3c00000| Untracked 
|  61|0x00000000c3d00000, 0x00000000c3d00000, 0x00000000c3e00000|  0%| F|  |TAMS 0x00000000c3d00000| PB 0x00000000c3d00000| Untracked 
|  62|0x00000000c3e00000, 0x00000000c3e00000, 0x00000000c3f00000|  0%| F|  |TAMS 0x00000000c3e00000| PB 0x00000000c3e00000| Untracked 
|  63|0x00000000c3f00000, 0x00000000c3f00000, 0x00000000c4000000|  0%| F|  |TAMS 0x00000000c3f00000| PB 0x00000000c3f00000| Untracked 
|  64|0x00000000c4000000, 0x00000000c4000000, 0x00000000c4100000|  0%| F|  |TAMS 0x00000000c4000000| PB 0x00000000c4000000| Untracked 
|  65|0x00000000c4100000, 0x00000000c4100000, 0x00000000c4200000|  0%| F|  |TAMS 0x00000000c4100000| PB 0x00000000c4100000| Untracked 
|  66|0x00000000c4200000, 0x00000000c4200000, 0x00000000c4300000|  0%| F|  |TAMS 0x00000000c4200000| PB 0x00000000c4200000| Untracked 
|  67|0x00000000c4300000, 0x00000000c4300000, 0x00000000c4400000|  0%| F|  |TAMS 0x00000000c4300000| PB 0x00000000c4300000| Untracked 
|  68|0x00000000c4400000, 0x00000000c4400000, 0x00000000c4500000|  0%| F|  |TAMS 0x00000000c4400000| PB 0x00000000c4400000| Untracked 
|  69|0x00000000c4500000, 0x00000000c4500000, 0x00000000c4600000|  0%| F|  |TAMS 0x00000000c4500000| PB 0x00000000c4500000| Untracked 
|  70|0x00000000c4600000, 0x00000000c4600000, 0x00000000c4700000|  0%| F|  |TAMS 0x00000000c4600000| PB 0x00000000c4600000| Untracked 
|  71|0x00000000c4700000, 0x00000000c4700000, 0x00000000c4800000|  0%| F|  |TAMS 0x00000000c4700000| PB 0x00000000c4700000| Untracked 
|  72|0x00000000c4800000, 0x00000000c4800000, 0x00000000c4900000|  0%| F|  |TAMS 0x00000000c4800000| PB 0x00000000c4800000| Untracked 
|  73|0x00000000c4900000, 0x00000000c4900000, 0x00000000c4a00000|  0%| F|  |TAMS 0x00000000c4900000| PB 0x00000000c4900000| Untracked 
|  74|0x00000000c4a00000, 0x00000000c4a00000, 0x00000000c4b00000|  0%| F|  |TAMS 0x00000000c4a00000| PB 0x00000000c4a00000| Untracked 
|  75|0x00000000c4b00000, 0x00000000c4b00000, 0x00000000c4c00000|  0%| F|  |TAMS 0x00000000c4b00000| PB 0x00000000c4b00000| Untracked 
|  76|0x00000000c4c00000, 0x00000000c4c00000, 0x00000000c4d00000|  0%| F|  |TAMS 0x00000000c4c00000| PB 0x00000000c4c00000| Untracked 
|  77|0x00000000c4d00000, 0x00000000c4d00000, 0x00000000c4e00000|  0%| F|  |TAMS 0x00000000c4d00000| PB 0x00000000c4d00000| Untracked 
|  78|0x00000000c4e00000, 0x00000000c4ef7c78, 0x00000000c4f00000| 96%| E|  |TAMS 0x00000000c4e00000| PB 0x00000000c4e00000| Complete 
| 248|0x00000000cf800000, 0x00000000cf833720, 0x00000000cf900000| 20%| S|CS|TAMS 0x00000000cf800000| PB 0x00000000cf800000| Complete 
| 249|0x00000000cf900000, 0x00000000cfa00000, 0x00000000cfa00000|100%| S|CS|TAMS 0x00000000cf900000| PB 0x00000000cf900000| Complete 
| 250|0x00000000cfa00000, 0x00000000cfb00000, 0x00000000cfb00000|100%| S|CS|TAMS 0x00000000cfa00000| PB 0x00000000cfa00000| Complete 
| 251|0x00000000cfb00000, 0x00000000cfc00000, 0x00000000cfc00000|100%| S|CS|TAMS 0x00000000cfb00000| PB 0x00000000cfb00000| Complete 
| 252|0x00000000cfc00000, 0x00000000cfd00000, 0x00000000cfd00000|100%| S|CS|TAMS 0x00000000cfc00000| PB 0x00000000cfc00000| Complete 
| 253|0x00000000cfd00000, 0x00000000cfe00000, 0x00000000cfe00000|100%| S|CS|TAMS 0x00000000cfd00000| PB 0x00000000cfd00000| Complete 
| 254|0x00000000cfe00000, 0x00000000cff00000, 0x00000000cff00000|100%| S|CS|TAMS 0x00000000cfe00000| PB 0x00000000cfe00000| Complete 
| 255|0x00000000cff00000, 0x00000000d0000000, 0x00000000d0000000|100%| S|CS|TAMS 0x00000000cff00000| PB 0x00000000cff00000| Complete 
| 256|0x00000000d0000000, 0x00000000d0100000, 0x00000000d0100000|100%| S|CS|TAMS 0x00000000d0000000| PB 0x00000000d0000000| Complete 
| 257|0x00000000d0100000, 0x00000000d0200000, 0x00000000d0200000|100%| S|CS|TAMS 0x00000000d0100000| PB 0x00000000d0100000| Complete 
| 258|0x00000000d0200000, 0x00000000d0300000, 0x00000000d0300000|100%| S|CS|TAMS 0x00000000d0200000| PB 0x00000000d0200000| Complete 
| 259|0x00000000d0300000, 0x00000000d0400000, 0x00000000d0400000|100%| S|CS|TAMS 0x00000000d0300000| PB 0x00000000d0300000| Complete 
| 260|0x00000000d0400000, 0x00000000d0500000, 0x00000000d0500000|100%| S|CS|TAMS 0x00000000d0400000| PB 0x00000000d0400000| Complete 
| 350|0x00000000d5e00000, 0x00000000d5f00000, 0x00000000d5f00000|100%| E|CS|TAMS 0x00000000d5e00000| PB 0x00000000d5e00000| Complete 
| 351|0x00000000d5f00000, 0x00000000d6000000, 0x00000000d6000000|100%| E|CS|TAMS 0x00000000d5f00000| PB 0x00000000d5f00000| Complete 

Card table byte_map: [0x0000023c7f3d0000,0x0000023c7f5d0000] _byte_map_base: 0x0000023c7edd0000

Marking Bits: (CMBitMap*) 0x0000023c7ee11150
 Bits: [0x0000023c173a0000, 0x0000023c183a0000)

Polling page: 0x0000023c7cd20000

Metaspace:

Usage:
  Non-class:     17.41 MB used.
      Class:      2.94 MB used.
       Both:     20.36 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      17.81 MB ( 28%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.25 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      21.06 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  14.09 MB
       Class:  12.77 MB
        Both:  26.86 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.12 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 1014.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 337.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1822.
num_chunk_merges: 3.
num_chunk_splits: 1192.
num_chunks_enlarged: 802.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1756Kb max_used=1756Kb free=118243Kb
 bounds [0x0000023c0fe70000, 0x0000023c100e0000, 0x0000023c173a0000]
CodeHeap 'profiled nmethods': size=120000Kb used=6374Kb max_used=6374Kb free=113625Kb
 bounds [0x0000023c083a0000, 0x0000023c089e0000, 0x0000023c0f8d0000]
CodeHeap 'non-nmethods': size=5760Kb used=1497Kb max_used=1545Kb free=4262Kb
 bounds [0x0000023c0f8d0000, 0x0000023c0fb40000, 0x0000023c0fe70000]
 total_blobs=3689 nmethods=3115 adapters=478
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 7.174 Thread 0x0000023c63a3a520 3029       4       java.lang.invoke.MethodType::equals (56 bytes)
Event: 7.180 Thread 0x0000023c63a3a520 nmethod 3029 0x0000023c10021410 code [0x0000023c100215c0, 0x0000023c10021948]
Event: 7.180 Thread 0x0000023c63a3a520 3026       4       jdk.internal.org.objectweb.asm.Type::getTypeInternal (218 bytes)
Event: 7.183 Thread 0x0000023c63a3a520 nmethod 3026 0x0000023c10021c10 code [0x0000023c10021dc0, 0x0000023c10022040]
Event: 7.190 Thread 0x0000023c1a59a750 3043       3       java.lang.ref.ReferenceQueue::enqueue0 (98 bytes)
Event: 7.190 Thread 0x0000023c63a3a520 3044       4       java.util.concurrent.locks.ReentrantLock::unlock (10 bytes)
Event: 7.190 Thread 0x0000023c1a59a750 nmethod 3043 0x0000023c089bea10 code [0x0000023c089bec00, 0x0000023c089bf230]
Event: 7.190 Thread 0x0000023c1a59a750 3045       3       java.util.concurrent.locks.ReentrantLock$NonfairSync::initialTryLock (58 bytes)
Event: 7.191 Thread 0x0000023c1a59a750 nmethod 3045 0x0000023c089bf310 code [0x0000023c089bf500, 0x0000023c089bf8f0]
Event: 7.191 Thread 0x0000023c1a59a750 3046       3       java.lang.ref.Reference::enqueueFromPending (19 bytes)
Event: 7.191 Thread 0x0000023c1a59a750 nmethod 3046 0x0000023c089bfa10 code [0x0000023c089bfbc0, 0x0000023c089bfde0]
Event: 7.191 Thread 0x0000023c1a59a750 3048   !   3       java.lang.ref.NativeReferenceQueue::enqueue (20 bytes)
Event: 7.191 Thread 0x0000023c1a59a750 nmethod 3048 0x0000023c089bfe90 code [0x0000023c089c0040, 0x0000023c089c0270]
Event: 7.191 Thread 0x0000023c1a59a750 3049       3       java.lang.ref.NativeReferenceQueue::signal (8 bytes)
Event: 7.191 Thread 0x0000023c1a59a750 nmethod 3049 0x0000023c089c0390 code [0x0000023c089c0540, 0x0000023c089c0680]
Event: 7.191 Thread 0x0000023c1a59a750 3051       3       java.lang.ref.ReferenceQueue::poll0 (65 bytes)
Event: 7.192 Thread 0x0000023c1a59a750 nmethod 3051 0x0000023c089c0790 code [0x0000023c089c0960, 0x0000023c089c0e78]
Event: 7.192 Thread 0x0000023c63a3a520 nmethod 3044 0x0000023c10022610 code [0x0000023c100227c0, 0x0000023c10022998]
Event: 7.192 Thread 0x0000023c63a3a520 3050       4       java.util.concurrent.locks.ReentrantLock::lock (8 bytes)
Event: 7.195 Thread 0x0000023c63a3a520 nmethod 3050 0x0000023c10022b10 code [0x0000023c10022cc0, 0x0000023c10022ef0]

GC Heap History (8 events):
Event: 0.591 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 360448K, used 22528K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 1070K, committed 1216K, reserved 1114112K
  class space    used 77K, committed 128K, reserved 1048576K
}
Event: 0.593 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 360448K, used 3541K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 1070K, committed 1216K, reserved 1114112K
  class space    used 77K, committed 128K, reserved 1048576K
}
Event: 2.251 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 360448K, used 39381K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 3 survivors (3072K)
 Metaspace       used 4397K, committed 4608K, reserved 1114112K
  class space    used 542K, committed 640K, reserved 1048576K
}
Event: 2.255 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 360448K, used 11374K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 4397K, committed 4608K, reserved 1114112K
  class space    used 542K, committed 640K, reserved 1048576K
}
Event: 3.471 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 360448K, used 62574K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 53 young (54272K), 5 survivors (5120K)
 Metaspace       used 5834K, committed 6144K, reserved 1114112K
  class space    used 756K, committed 896K, reserved 1048576K
}
Event: 3.475 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 360448K, used 18071K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 5834K, committed 6144K, reserved 1114112K
  class space    used 756K, committed 896K, reserved 1048576K
}
Event: 7.183 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 360448K, used 104087K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 91 young (93184K), 6 survivors (6144K)
 Metaspace       used 20799K, committed 21504K, reserved 1114112K
  class space    used 3001K, committed 3328K, reserved 1048576K
}
Event: 7.190 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 360448K, used 24805K [0x00000000c0000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 20799K, committed 21504K, reserved 1114112K
  class space    used 3001K, committed 3328K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.007 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.046 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.136 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.142 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.147 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.150 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.152 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.480 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.659 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 1.097 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 1.101 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 3.579 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 3.582 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 4.002 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 4.201 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 5.433 Thread 0x0000023c61a0f8d0 DEOPT PACKING pc=0x0000023c0ffb897c sp=0x000000a54f9fd1e0
Event: 5.433 Thread 0x0000023c61a0f8d0 DEOPT UNPACKING pc=0x0000023c0f9246a2 sp=0x000000a54f9fd170 mode 2
Event: 6.318 Thread 0x0000023c61a0f8d0 DEOPT PACKING pc=0x0000023c0848bfdf sp=0x000000a54f9fb130
Event: 6.318 Thread 0x0000023c61a0f8d0 DEOPT UNPACKING pc=0x0000023c0f924e42 sp=0x000000a54f9fa5c8 mode 0
Event: 6.875 Thread 0x0000023c61a0f8d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023c10004dbc relative=0x000000000000011c
Event: 6.875 Thread 0x0000023c61a0f8d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023c10004dbc method=java.util.Arrays.equals([Ljava/lang/Object;[Ljava/lang/Object;)Z @ 44 c2
Event: 6.875 Thread 0x0000023c61a0f8d0 DEOPT PACKING pc=0x0000023c10004dbc sp=0x000000a54f9fb7b0
Event: 6.875 Thread 0x0000023c61a0f8d0 DEOPT UNPACKING pc=0x0000023c0f9246a2 sp=0x000000a54f9fb750 mode 2
Event: 6.875 Thread 0x0000023c61a0f8d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023c10012110 relative=0x00000000000002b0
Event: 6.875 Thread 0x0000023c61a0f8d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023c10012110 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 131 c2
Event: 6.875 Thread 0x0000023c61a0f8d0 DEOPT PACKING pc=0x0000023c10012110 sp=0x000000a54f9fb870
Event: 6.875 Thread 0x0000023c61a0f8d0 DEOPT UNPACKING pc=0x0000023c0f9246a2 sp=0x000000a54f9fb7e0 mode 2
Event: 6.875 Thread 0x0000023c61a0f8d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023c100107f8 relative=0x0000000000000758
Event: 6.875 Thread 0x0000023c61a0f8d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023c100107f8 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 206 c2
Event: 6.875 Thread 0x0000023c61a0f8d0 DEOPT PACKING pc=0x0000023c100107f8 sp=0x000000a54f9fb7e0
Event: 6.875 Thread 0x0000023c61a0f8d0 DEOPT UNPACKING pc=0x0000023c0f9246a2 sp=0x000000a54f9fb768 mode 2
Event: 7.190 Thread 0x0000023c1a57ddc0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023c0ff5f61c relative=0x000000000000017c
Event: 7.190 Thread 0x0000023c1a57ddc0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023c0ff5f61c method=java.util.concurrent.locks.ReentrantLock$NonfairSync.initialTryLock()Z @ 25 c2
Event: 7.190 Thread 0x0000023c1a57ddc0 DEOPT PACKING pc=0x0000023c0ff5f61c sp=0x000000a54e1fef30
Event: 7.190 Thread 0x0000023c1a57ddc0 DEOPT UNPACKING pc=0x0000023c0f9246a2 sp=0x000000a54e1feed0 mode 2

Classes loaded (20 events):
Event: 6.366 Loading class java/io/ObjectInput done
Event: 6.366 Loading class java/io/ObjectStreamConstants
Event: 6.366 Loading class java/io/ObjectStreamConstants done
Event: 6.366 Loading class java/io/ObjectInputStream done
Event: 6.369 Loading class java/io/ObjectOutputStream
Event: 6.370 Loading class java/io/ObjectOutput
Event: 6.370 Loading class java/io/ObjectOutput done
Event: 6.370 Loading class java/io/ObjectOutputStream done
Event: 6.784 Loading class javax/xml/parsers/ParserConfigurationException
Event: 6.784 Loading class javax/xml/parsers/ParserConfigurationException done
Event: 6.784 Loading class org/xml/sax/SAXException
Event: 6.784 Loading class org/xml/sax/SAXException done
Event: 6.784 Loading class javax/xml/xpath/XPathExpressionException
Event: 6.784 Loading class javax/xml/xpath/XPathException
Event: 6.785 Loading class javax/xml/xpath/XPathException done
Event: 6.785 Loading class javax/xml/xpath/XPathExpressionException done
Event: 6.785 Loading class org/xml/sax/ErrorHandler
Event: 6.785 Loading class org/xml/sax/ErrorHandler done
Event: 6.937 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 6.937 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 5.178 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2e10518}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d2e10518) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.297 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2c00210}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.O
Event: 5.307 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2c6c950}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d2c6c950) 
thrown [s\open\src\hotspot\share\interprete
Event: 5.311 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2c953b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d2c953b8) 
thrown [s\open\src\hotspo
Event: 5.315 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2cbee68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d2cbee68) 
thrown 
Event: 5.319 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2ce9d80}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000
Event: 5.322 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2b167b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 5.327 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2b43948}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 5.338 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2b71588}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 5.341 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2ba03c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 5.348 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2bcfb20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 5.351 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d2400fd0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 6.300 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d1cb02d0}: Found class java.lang.Object, but interface was expected> (0x00000000d1cb02d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.329 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d1cdc590}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d1cdc590) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.401 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d1bfe440}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d1bfe440) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.440 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d1aba270}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d1aba270) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.599 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d1461140}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Ob
Event: 6.609 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d14d5eb8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Ob
Event: 6.902 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d0b531d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d0b531d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.974 Thread 0x0000023c61a0f8d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d0960698}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d0960698) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3.877 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.971 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.971 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.086 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.086 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.182 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.182 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.527 Executing VM operation: ICBufferFull
Event: 4.528 Executing VM operation: ICBufferFull done
Event: 4.872 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.872 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.396 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.396 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.397 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.397 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.397 Executing VM operation: Cleanup
Event: 6.398 Executing VM operation: Cleanup done
Event: 7.183 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 7.190 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 7.198 Executing VM operation: G1PauseRemark

Events (20 events):
Event: 4.049 Thread 0x0000023c1a722880 Thread added: 0x0000023c1a722880
Event: 4.060 Thread 0x0000023c618f84d0 Thread added: 0x0000023c618f84d0
Event: 4.167 Thread 0x0000023c1a720120 Thread added: 0x0000023c1a720120
Event: 4.248 Thread 0x0000023c1a7214d0 Thread added: 0x0000023c1a7214d0
Event: 4.252 Thread 0x0000023c1a7221f0 Thread added: 0x0000023c1a7221f0
Event: 4.306 Thread 0x0000023c61a0aa10 Thread added: 0x0000023c61a0aa10
Event: 4.316 Thread 0x0000023c61a0f8d0 Thread added: 0x0000023c61a0f8d0
Event: 4.333 Thread 0x0000023c61a0cae0 Thread added: 0x0000023c61a0cae0
Event: 4.339 Thread 0x0000023c61a0d170 Thread added: 0x0000023c61a0d170
Event: 4.342 Thread 0x0000023c61a0b730 Thread added: 0x0000023c61a0b730
Event: 4.667 Thread 0x0000023c618f84d0 Thread exited: 0x0000023c618f84d0
Event: 4.873 Thread 0x0000023c61a08fd0 Thread added: 0x0000023c61a08fd0
Event: 4.888 Thread 0x0000023c61a0d800 Thread added: 0x0000023c61a0d800
Event: 4.930 Thread 0x0000023c61bfcdd0 Thread added: 0x0000023c61bfcdd0
Event: 4.950 Thread 0x0000023c61a0b0a0 Thread added: 0x0000023c61a0b0a0
Event: 5.315 Thread 0x0000023c62410240 Thread added: 0x0000023c62410240
Event: 6.294 Thread 0x0000023c62410240 Thread exited: 0x0000023c62410240
Event: 6.663 Thread 0x0000023c61bfcdd0 Thread exited: 0x0000023c61bfcdd0
Event: 6.719 Thread 0x0000023c61a0c450 Thread added: 0x0000023c61a0c450
Event: 7.170 Thread 0x0000023c63a3a520 Thread added: 0x0000023c63a3a520


Dynamic libraries:
0x00007ff6d9320000 - 0x00007ff6d9330000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007fffbd670000 - 0x00007fffbd868000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffbd310000 - 0x00007fffbd3d2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffbb2f0000 - 0x00007fffbb5e6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffbae80000 - 0x00007fffbaf80000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fffa6650000 - 0x00007fffa6669000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007fff9e270000 - 0x00007fff9e28b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007fffbc4a0000 - 0x00007fffbc551000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffbd0e0000 - 0x00007fffbd17e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffbd590000 - 0x00007fffbd62f000 	C:\WINDOWS\System32\sechost.dll
0x00007fffbd3e0000 - 0x00007fffbd503000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffbae50000 - 0x00007fffbae77000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fffbc630000 - 0x00007fffbc7cd000 	C:\WINDOWS\System32\USER32.dll
0x00007fffbae20000 - 0x00007fffbae42000 	C:\WINDOWS\System32\win32u.dll
0x00007fffaa920000 - 0x00007fffaabba000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007fffbcf00000 - 0x00007fffbcf2b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffbad00000 - 0x00007fffbae19000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffbb5f0000 - 0x00007fffbb68d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fffb29a0000 - 0x00007fffb29aa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffbb720000 - 0x00007fffbb74f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fffb70d0000 - 0x00007fffb70dc000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007fff8fdb0000 - 0x00007fff8fe3e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007fff62790000 - 0x00007fff634a7000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007fffbc8b0000 - 0x00007fffbc91b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fffbab60000 - 0x00007fffbabab000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fffafb00000 - 0x00007fffafb27000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffbab40000 - 0x00007fffbab52000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fffb9570000 - 0x00007fffb9582000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fffb41d0000 - 0x00007fffb41da000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007fffb8b50000 - 0x00007fffb8d51000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffa9960000 - 0x00007fffa9994000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffbb050000 - 0x00007fffbb0d2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffa6350000 - 0x00007fffa635f000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007fff9e250000 - 0x00007fff9e26f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007fffbbab0000 - 0x00007fffbc21e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffb8d60000 - 0x00007fffb9503000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffbb750000 - 0x00007fffbbaa3000 	C:\WINDOWS\System32\combase.dll
0x00007fffba660000 - 0x00007fffba68b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007fffbc560000 - 0x00007fffbc62d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fffbc3f0000 - 0x00007fffbc49d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffbd2b0000 - 0x00007fffbd30b000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffbac30000 - 0x00007fffbac55000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff8e940000 - 0x00007fff8ea17000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007fff9a350000 - 0x00007fff9a368000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007fffb2d80000 - 0x00007fffb2d90000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007fffb6ef0000 - 0x00007fffb6ffa000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffba3c0000 - 0x00007fffba42a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff9cc10000 - 0x00007fff9cc26000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007fffb7d40000 - 0x00007fffb7d50000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007fff91960000 - 0x00007fff91987000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007fff8eb80000 - 0x00007fff8ecc4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007fffaa640000 - 0x00007fffaa64a000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007fffaa5d0000 - 0x00007fffaa5db000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007fffbb690000 - 0x00007fffbb698000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fffba5b0000 - 0x00007fffba5c8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffb9ce0000 - 0x00007fffb9d18000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffbabb0000 - 0x00007fffbabde000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffba5d0000 - 0x00007fffba5dc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffba0a0000 - 0x00007fffba0db000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffbca20000 - 0x00007fffbca28000 	C:\WINDOWS\System32\NSI.dll
0x00007fffa6360000 - 0x00007fffa6369000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007fffa98d0000 - 0x00007fffa98de000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007fffbb190000 - 0x00007fffbb2ed000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fffba6d0000 - 0x00007fffba6f7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fffba690000 - 0x00007fffba6cb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fffa0080000 - 0x00007fffa0087000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx1024m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 643825664                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 10:19 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4501M free)
TotalPageFile size 22476M (AvailPageFile size 263M)
current process WorkingSet (physical memory assigned to process): 169M, peak: 253M
current process commit charge ("private bytes"): 279M, peak: 551M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
