package com.mdsadrulhasan.appy99lisence.fragments;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.appy99lisence.R;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomDbHelper;
import com.mdsadrulhasan.appy99lisence.telecom.TelecomLogAdapter;

public class DashboardMonitorFragment extends Fragment {

    private static final String TAG = "DashboardMonitorFragment";
    private static final int REFRESH_INTERVAL = 5000; // 5 seconds

    // UI Components
    private TextView monitorStatusIndicator;
    private RecyclerView monitorLogRecycler;
    private Button refreshMonitorButton;
    private Button clearLogsButton;

    private SharedPreferences preferences;
    private TelecomDbHelper dbHelper;
    private TelecomLogAdapter logAdapter;
    private Handler refreshHandler;
    private Runnable refreshRunnable;

    public static DashboardMonitorFragment newInstance() {
        return new DashboardMonitorFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        preferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
        dbHelper = new TelecomDbHelper(requireContext());
        refreshHandler = new Handler(Looper.getMainLooper());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_telecom_monitor, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initializeViews(view);
        setupClickListeners();
        setupRecyclerView();
        loadMonitorData();
        startAutoRefresh();
    }

    private void initializeViews(View view) {
        monitorStatusIndicator = view.findViewById(R.id.monitor_status_indicator);
        monitorLogRecycler = view.findViewById(R.id.monitor_log_recycler);
        refreshMonitorButton = view.findViewById(R.id.refresh_monitor_button);
        clearLogsButton = view.findViewById(R.id.clear_logs_button);
    }

    private void setupClickListeners() {
        if (refreshMonitorButton != null) {
            refreshMonitorButton.setOnClickListener(v -> refreshMonitorData());
        }

        if (clearLogsButton != null) {
            clearLogsButton.setOnClickListener(v -> clearActivityLogs());
        }
    }

    private void setupRecyclerView() {
        if (monitorLogRecycler != null) {
            monitorLogRecycler.setLayoutManager(new LinearLayoutManager(requireContext()));
            logAdapter = new TelecomLogAdapter(requireContext());
            monitorLogRecycler.setAdapter(logAdapter);
        }
    }

    private void loadMonitorData() {
        updateSystemStatus();
        updateActivityLogs();
    }

    private void updateSystemStatus() {
        if (monitorStatusIndicator != null) {
            boolean telecomServiceRunning = preferences.getBoolean("telecom_service_running", false);
            boolean rechargeServiceRunning = preferences.getBoolean("recharge_service_running", false);

            String status;
            if (telecomServiceRunning && rechargeServiceRunning) {
                status = "🟢 Active";
                monitorStatusIndicator.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            } else if (telecomServiceRunning || rechargeServiceRunning) {
                status = "🟡 Partial";
                monitorStatusIndicator.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            } else {
                status = "🔴 Inactive";
                monitorStatusIndicator.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }

            monitorStatusIndicator.setText(status);
        }
    }

    private void updateActivityLogs() {
        if (logAdapter != null && dbHelper != null) {
            // Load recent logs (last 50)
            logAdapter.updateLogs(dbHelper.getRecentLogs(50));
        }
    }

    private void refreshMonitorData() {
        Log.d(TAG, "Refreshing monitor data");
        loadMonitorData();
    }

    private void clearActivityLogs() {
        if (dbHelper != null) {
            try {
                dbHelper.clearAllLogs();
                updateActivityLogs();
                Log.d(TAG, "Activity logs cleared");
            } catch (Exception e) {
                Log.e(TAG, "Error clearing activity logs", e);
            }
        }
    }

    private void startAutoRefresh() {
        refreshRunnable = new Runnable() {
            @Override
            public void run() {
                if (isAdded() && !isDetached()) {
                    loadMonitorData();
                    refreshHandler.postDelayed(this, REFRESH_INTERVAL);
                }
            }
        };
        refreshHandler.postDelayed(refreshRunnable, REFRESH_INTERVAL);
    }

    private void stopAutoRefresh() {
        if (refreshHandler != null && refreshRunnable != null) {
            refreshHandler.removeCallbacks(refreshRunnable);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh data when fragment becomes visible
        loadMonitorData();
        startAutoRefresh();
    }

    @Override
    public void onPause() {
        super.onPause();
        // Stop auto refresh when fragment is not visible
        stopAutoRefresh();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stopAutoRefresh();
        if (dbHelper != null) {
            dbHelper.close();
        }
    }
}
