package com.appystore.mrecharge;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class RecyclerviewItemAdapter extends RecyclerView.Adapter<RecyclerviewItemAdapter.MyViewHolder> {

    private final Context context;
    private final List<Items> itemsList;

    public RecyclerviewItemAdapter(List<Items> list, Context context) {
        this.itemsList = list;
        this.context = context;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_todo, parent, false);
        return new MyViewHolder(view);
    }

    @Override
    public void onBindViewHolder(MyViewHolder holder, int position) {
        try {
            if (position < 0 || position >= itemsList.size()) {
                return;
            }

            Items item = itemsList.get(position);
            if (item == null) {
                return;
            }

            // Set status text and color based on response code
            switch (item.getResponse()) {
                case 0:
                    holder.name.setTextColor(ContextCompat.getColor(context, R.color.waiting));
                    holder.name.setText("Waiting...");
                    break;
                case 1:
                    holder.name.setTextColor(ContextCompat.getColor(context, R.color.done));
                    holder.name.setText("Done");
                    break;
                case 4:
                    holder.name.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                    holder.name.setText("Processing");
                    break;
                default:
                    holder.name.setTextColor(ContextCompat.getColor(context, R.color.failed));
                    holder.name.setText("Failed");
                    break;
            }

            // Set the details text
            if (item.getId() != null) {
                holder.price.setText(item.getId());
            } else {
                holder.price.setText("No details available");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return itemsList.size();
    }

    static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView name, price;

        MyViewHolder(View itemView) {
            super(itemView);
            name = itemView.findViewById(R.id.status);
            price = itemView.findViewById(R.id.tvPrice);
        }
    }
}
