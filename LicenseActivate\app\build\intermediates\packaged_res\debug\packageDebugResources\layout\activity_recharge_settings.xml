<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f5f5f5"
    tools:context=".recharge.RechargeSettingsActivity">

    <!-- Scrollable Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⚙️ Recharge Settings"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#2c3e50"
                    android:layout_marginBottom="8dp"
                    android:gravity="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Configure your recharge preferences"
                    android:textSize="14sp"
                    android:textColor="#7f8c8d"
                    android:gravity="center" />

            </LinearLayout>

            <!-- SIM Configuration Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📱 SIM Configuration"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="16dp" />

                    <!-- SIM 1 Operator -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📡 SIM 1 Operator"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/sim1OperatorSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- SIM 2 Operator -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📡 SIM 2 Operator"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/sim2OperatorSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- Auto Recharge Checkbox -->
                    <CheckBox
                        android:id="@+id/autoRechargeCheckbox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔄 Enable automatic recharge processing"
                        android:textSize="14sp"
                        android:textColor="#34495e" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Server Configuration Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🌐 Server Configuration"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="16dp" />

                    <!-- Server URL Field -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔗 Server URL"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/serverUrlField"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:hint="Enter server URL"
                        android:inputType="textUri"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- API PIN Field -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔑 API PIN"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/apiPinField"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:hint="Enter API PIN"
                        android:inputType="textPassword"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- Check Interval Field -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏱️ Check Interval (seconds)"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/checkIntervalField"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:hint="Check interval in seconds"
                        android:inputType="number"
                        android:text="5"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Service Control Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎛️ Service Control"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="16dp" />

                    <!-- Service Control Buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <Button
                            android:id="@+id/startServiceButton"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="▶️ Start Service"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#ffffff"
                            android:background="@drawable/button_background"
                            android:elevation="2dp" />

                        <Button
                            android:id="@+id/stopServiceButton"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="⏹️ Stop Service"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#ffffff"
                            android:background="@drawable/button_background"
                            android:elevation="2dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:elevation="4dp"
                android:radius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📊 Status"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="#34495e"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Loading settings..."
                        android:textSize="14sp"
                        android:textColor="#2c3e50"
                        android:background="#ecf0f1"
                        android:padding="12dp"
                        android:radius="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2"
                android:layout_marginBottom="24dp">

                <Button
                    android:id="@+id/saveSettingsButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="💾 Save Settings"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_background"
                    android:elevation="2dp" />

                <Button
                    android:id="@+id/backButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="🔙 Back"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#ffffff"
                    android:background="@drawable/button_background"
                    android:elevation="2dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Footer Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#f5f5f5"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚙️ Recharge Settings - License Management System"
            android:textSize="12sp"
            android:textColor="#7f8c8d"
            android:gravity="center"
            android:padding="8dp" />

    </LinearLayout>

</LinearLayout>
