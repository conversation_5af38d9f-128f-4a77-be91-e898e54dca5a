package com.mdsadrulhasan.appy99lisence.telecom;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.telephony.SmsMessage;
import android.util.Log;

/**
 * SMS receiver for handling telecom recharge SMS responses
 * Integrated from backup/app project into LicenseActivate
 */
public class TelecomSmsReceiver extends BroadcastReceiver {
    
    private static final String TAG = "TelecomSmsReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            if (intent == null || intent.getAction() == null) {
                return;
            }
            
            // Check if this is an SMS received intent
            if (!"android.provider.Telephony.SMS_RECEIVED".equals(intent.getAction())) {
                return;
            }
            
            Log.d(TAG, "SMS received, processing...");
            
            // Extract SMS messages from intent
            Bundle extras = intent.getExtras();
            if (extras == null) {
                return;
            }
            
            Object[] pdus = (Object[]) extras.get("pdus");
            if (pdus == null || pdus.length == 0) {
                return;
            }
            
            // Process each SMS message
            StringBuilder messageBody = new StringBuilder();
            String sender = null;
            
            for (Object pdu : pdus) {
                SmsMessage smsMessage = SmsMessage.createFromPdu((byte[]) pdu);
                if (smsMessage != null) {
                    messageBody.append(smsMessage.getMessageBody());
                    if (sender == null) {
                        sender = smsMessage.getOriginatingAddress();
                    }
                }
            }
            
            String fullMessage = messageBody.toString();
            
            if (sender != null && !fullMessage.isEmpty()) {
                Log.d(TAG, "Received SMS from: " + sender + ", Message: " + fullMessage);
                
                // Check if this SMS is related to recharge operations
                if (isRechargeRelatedSms(sender, fullMessage)) {
                    processSmsMessage(context, sender, fullMessage);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS", e);
        }
    }
    
    /**
     * Check if SMS is related to recharge operations
     */
    private boolean isRechargeRelatedSms(String sender, String message) {
        if (sender == null || message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        String lowerSender = sender.toLowerCase();
        
        // Check for telecom operator senders
        if (lowerSender.contains("grameenphone") ||
            lowerSender.contains("robi") ||
            lowerSender.contains("airtel") ||
            lowerSender.contains("banglalink") ||
            lowerSender.contains("teletalk") ||
            lowerSender.contains("bkash") ||
            lowerSender.contains("rocket") ||
            lowerSender.contains("nagad") ||
            lowerSender.contains("16247") ||
            lowerSender.contains("16222") ||
            lowerSender.contains("16216") ||
            lowerSender.contains("16212")) {
            return true;
        }
        
        // Check for recharge-related keywords in message
        if (lowerMessage.contains("recharge") ||
            lowerMessage.contains("topup") ||
            lowerMessage.contains("balance") ||
            lowerMessage.contains("successful") ||
            lowerMessage.contains("failed") ||
            lowerMessage.contains("completed") ||
            lowerMessage.contains("credited") ||
            lowerMessage.contains("debited") ||
            lowerMessage.contains("tk") ||
            lowerMessage.contains("taka") ||
            lowerMessage.contains("bdt")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Process the recharge-related SMS message
     */
    private void processSmsMessage(Context context, String sender, String message) {
        try {
            // Get device ID for logging
            String deviceId = getPreference(context, "device_id", "");
            
            // Save SMS to local database
            TelecomDbHelper dbHelper = new TelecomDbHelper(context);
            
            // Try to extract order ID from message if possible
            String orderId = extractOrderIdFromMessage(message);
            
            // Insert SMS log
            boolean smsInserted = dbHelper.insertSMS(message, sender, "received");
            
            Log.d(TAG, "SMS saved to database: " + smsInserted);
            
            // Send to server for processing
            TelecomApiClient apiClient = new TelecomApiClient(context);
            apiClient.sendRechargeResponse(orderId != null ? orderId : "unknown", 
                                         message, 
                                         determineStatusFromMessage(message), 
                                         new TelecomApiClient.ApiCallback() {
                @Override
                public void onSuccess(String response) {
                    Log.d(TAG, "SMS response sent to server successfully");
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "Error sending SMS response to server: " + error);
                }
            });
            
            // If we found an order ID, update the corresponding recharge record
            if (orderId != null && !orderId.isEmpty()) {
                String status = determineStatusFromMessage(message);
                dbHelper.updateRechargeStatus(orderId, status, message);
                Log.d(TAG, "Updated recharge status for order: " + orderId + " to: " + status);
            }
            
            // Clean up
            dbHelper.close();
            apiClient.cleanup();
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS message", e);
        }
    }
    
    /**
     * Try to extract order ID from SMS message
     */
    private String extractOrderIdFromMessage(String message) {
        if (message == null) {
            return null;
        }
        
        try {
            // Look for common order ID patterns
            // Pattern 1: Order ID: 123456
            if (message.contains("Order ID:") || message.contains("order id:")) {
                String[] parts = message.split("(?i)order id:");
                if (parts.length > 1) {
                    String orderPart = parts[1].trim();
                    String[] orderWords = orderPart.split("\\s+");
                    if (orderWords.length > 0) {
                        return orderWords[0].replaceAll("[^0-9]", "");
                    }
                }
            }
            
            // Pattern 2: Ref: 123456 or Reference: 123456
            if (message.contains("Ref:") || message.contains("Reference:")) {
                String[] parts = message.split("(?i)(ref:|reference:)");
                if (parts.length > 1) {
                    String refPart = parts[1].trim();
                    String[] refWords = refPart.split("\\s+");
                    if (refWords.length > 0) {
                        return refWords[0].replaceAll("[^0-9]", "");
                    }
                }
            }
            
            // Pattern 3: Transaction ID or TxnID
            if (message.contains("Transaction ID:") || message.contains("TxnID:")) {
                String[] parts = message.split("(?i)(transaction id:|txnid:)");
                if (parts.length > 1) {
                    String txnPart = parts[1].trim();
                    String[] txnWords = txnPart.split("\\s+");
                    if (txnWords.length > 0) {
                        return txnWords[0].replaceAll("[^0-9]", "");
                    }
                }
            }
            
            // Pattern 4: Look for any number sequence that might be an order ID
            String[] words = message.split("\\s+");
            for (String word : words) {
                String numbers = word.replaceAll("[^0-9]", "");
                if (numbers.length() >= 6 && numbers.length() <= 12) {
                    return numbers;
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error extracting order ID from message", e);
        }
        
        return null;
    }
    
    /**
     * Determine status from SMS message content
     */
    private String determineStatusFromMessage(String message) {
        if (message == null) {
            return "unknown";
        }
        
        String lowerMessage = message.toLowerCase();
        
        // Success indicators
        if (lowerMessage.contains("successful") ||
            lowerMessage.contains("success") ||
            lowerMessage.contains("completed") ||
            lowerMessage.contains("credited") ||
            lowerMessage.contains("recharged") ||
            lowerMessage.contains("topup successful") ||
            lowerMessage.contains("balance added")) {
            return "completed";
        }
        
        // Failure indicators
        if (lowerMessage.contains("failed") ||
            lowerMessage.contains("failure") ||
            lowerMessage.contains("error") ||
            lowerMessage.contains("insufficient") ||
            lowerMessage.contains("invalid") ||
            lowerMessage.contains("declined") ||
            lowerMessage.contains("rejected")) {
            return "failed";
        }
        
        // Pending indicators
        if (lowerMessage.contains("pending") ||
            lowerMessage.contains("processing") ||
            lowerMessage.contains("in progress")) {
            return "processing";
        }
        
        // Default to completed if we can't determine
        return "completed";
    }
    
    /**
     * Get preference value
     */
    private String getPreference(Context context, String key, String defaultValue) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(key, defaultValue);
    }
}
