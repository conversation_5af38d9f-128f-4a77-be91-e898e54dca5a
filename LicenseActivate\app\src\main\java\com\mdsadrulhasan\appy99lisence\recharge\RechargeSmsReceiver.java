package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.telephony.SmsMessage;
import android.util.Log;

/**
 * SMS Broadcast Receiver for processing recharge response messages
 */
public class RechargeSmsReceiver extends BroadcastReceiver {
    
    private static final String TAG = "RechargeSmsReceiver";
    private static final String SMS_RECEIVED_ACTION = "android.provider.Telephony.SMS_RECEIVED";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || !SMS_RECEIVED_ACTION.equals(intent.getAction())) {
            return;
        }
        
        try {
            // Check if recharge module is enabled
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
            boolean rechargeEnabled = preferences.getBoolean("recharge_service_enabled", true);
            
            if (!rechargeEnabled) {
                Log.d(TAG, "Recharge service disabled, ignoring SMS");
                return;
            }
            
            // Extract SMS messages from intent
            Bundle bundle = intent.getExtras();
            if (bundle == null) {
                return;
            }
            
            Object[] pdus = (Object[]) bundle.get("pdus");
            if (pdus == null || pdus.length == 0) {
                return;
            }
            
            String format = bundle.getString("format");
            StringBuilder messageBody = new StringBuilder();
            String sender = null;
            
            // Process all SMS parts
            for (Object pdu : pdus) {
                SmsMessage smsMessage;
                
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    smsMessage = SmsMessage.createFromPdu((byte[]) pdu, format);
                } else {
                    smsMessage = SmsMessage.createFromPdu((byte[]) pdu);
                }
                
                if (smsMessage != null) {
                    if (sender == null) {
                        sender = smsMessage.getOriginatingAddress();
                    }
                    messageBody.append(smsMessage.getMessageBody());
                }
            }
            
            String fullMessage = messageBody.toString();
            
            if (sender != null && !fullMessage.isEmpty()) {
                Log.d(TAG, "Received SMS from: " + sender + ", Message: " + fullMessage);
                
                // Check if this SMS is related to recharge operations
                if (isRechargeRelatedSms(sender, fullMessage)) {
                    processSmsMessage(context, sender, fullMessage);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS", e);
        }
    }
    
    /**
     * Check if the SMS is related to recharge operations
     */
    private boolean isRechargeRelatedSms(String sender, String message) {
        if (sender == null || message == null) {
            return false;
        }
        
        // Normalize sender and message for checking
        String normalizedSender = sender.toLowerCase().trim();
        String normalizedMessage = message.toLowerCase().trim();
        
        // Check for known operator short codes and keywords
        String[] operatorCodes = {
            "121", "123", "124", "125", "126", // USSD response codes
            "gp", "grameenphone", "robi", "banglalink", "airtel", "teletalk",
            "16216", "8801", "88017" // Common operator short codes
        };
        
        String[] rechargeKeywords = {
            "recharge", "topup", "top-up", "balance", "successful", "failed",
            "completed", "processed", "amount", "taka", "tk", "bdt",
            "mobile", "prepaid", "postpaid", "account", "credited", "debited"
        };
        
        // Check sender against known operator codes
        for (String code : operatorCodes) {
            if (normalizedSender.contains(code)) {
                Log.d(TAG, "SMS from known operator: " + sender);
                return true;
            }
        }
        
        // Check message content for recharge keywords
        for (String keyword : rechargeKeywords) {
            if (normalizedMessage.contains(keyword)) {
                Log.d(TAG, "SMS contains recharge keyword: " + keyword);
                return true;
            }
        }
        
        // Check for USSD response patterns
        if (normalizedMessage.contains("*") && normalizedMessage.contains("#")) {
            Log.d(TAG, "SMS appears to be USSD response");
            return true;
        }
        
        return false;
    }
    
    /**
     * Process the recharge-related SMS message
     */
    private void processSmsMessage(Context context, String sender, String message) {
        try {
            // Get device ID for logging
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
            String deviceId = preferences.getString("device_id", "");
            
            // Save SMS to local database
            RechargeDbHelper dbHelper = new RechargeDbHelper(context);
            
            // Try to extract order ID from message if possible
            String orderId = extractOrderIdFromMessage(message);
            
            // Insert SMS log
            long smsLogId = dbHelper.insertSmsLog(deviceId, sender, message, orderId);
            
            Log.d(TAG, "SMS saved to database with ID: " + smsLogId);
            
            // Send to server for processing
            RechargeApiClient apiClient = new RechargeApiClient(context);
            apiClient.processSmsResponse(sender, message, orderId);
            
            // If we found an order ID, update the corresponding recharge record
            if (orderId != null && !orderId.isEmpty()) {
                String status = determineStatusFromMessage(message);
                dbHelper.updateRechargeStatus(orderId, status, null, message);
                Log.d(TAG, "Updated recharge status for order: " + orderId + " to: " + status);
            }
            
            // Clean up
            dbHelper.close();
            apiClient.cleanup();
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS message", e);
        }
    }
    
    /**
     * Try to extract order ID from SMS message
     */
    private String extractOrderIdFromMessage(String message) {
        if (message == null) {
            return null;
        }
        
        try {
            // Look for patterns like "RCH_123456" or "Order: RCH_123456"
            String[] patterns = {
                "RCH_\\d+",
                "Order[:\\s]+RCH_\\d+",
                "Ref[:\\s]+RCH_\\d+",
                "Transaction[:\\s]+RCH_\\d+"
            };
            
            for (String pattern : patterns) {
                java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
                java.util.regex.Matcher m = p.matcher(message);
                
                if (m.find()) {
                    String match = m.group();
                    // Extract just the RCH_XXXXXX part
                    java.util.regex.Pattern orderPattern = java.util.regex.Pattern.compile("RCH_\\d+", java.util.regex.Pattern.CASE_INSENSITIVE);
                    java.util.regex.Matcher orderMatcher = orderPattern.matcher(match);
                    
                    if (orderMatcher.find()) {
                        return orderMatcher.group();
                    }
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error extracting order ID from message", e);
        }
        
        return null;
    }
    
    /**
     * Determine transaction status from SMS message content
     */
    private String determineStatusFromMessage(String message) {
        if (message == null) {
            return "completed"; // Default status
        }
        
        String normalizedMessage = message.toLowerCase().trim();
        
        // Check for failure indicators
        String[] failureKeywords = {
            "failed", "error", "unsuccessful", "declined", "rejected",
            "insufficient", "invalid", "expired", "blocked", "cancelled"
        };
        
        for (String keyword : failureKeywords) {
            if (normalizedMessage.contains(keyword)) {
                return "failed";
            }
        }
        
        // Check for success indicators
        String[] successKeywords = {
            "successful", "completed", "processed", "credited", "confirmed",
            "done", "approved", "accepted", "received"
        };
        
        for (String keyword : successKeywords) {
            if (normalizedMessage.contains(keyword)) {
                return "completed";
            }
        }
        
        // Check for pending indicators
        String[] pendingKeywords = {
            "pending", "processing", "in progress", "being processed"
        };
        
        for (String keyword : pendingKeywords) {
            if (normalizedMessage.contains(keyword)) {
                return "processing";
            }
        }
        
        // Default to completed if no specific status found
        return "completed";
    }
}
