# 📱 Notification Permission Testing Guide

## 🎯 Testing Scenarios

### **Scenario 1: Fresh Install (First Time)**
1. **Install the app** on a device with Android 13+ (API 33+)
2. **Launch the app** for the first time
3. **Expected**: Permission explanation dialog should appear
4. **Tap "Grant Permission"**
5. **Expected**: System permission dialog appears
6. **Grant permission**
7. **Expected**: Success toast + test notification appears

### **Scenario 2: Permission Denial**
1. **Launch the app** (fresh install)
2. **Tap "Not Now"** in explanation dialog
3. **Expected**: App continues without notifications
4. **Try to activate license** (if applicable)
5. **Expected**: No notifications shown, but app functions normally

### **Scenario 3: Permanent Denial**
1. **Launch the app** and **deny permission** in system dialog
2. **Close and reopen** the app
3. **Expected**: Settings redirect dialog appears
4. **Tap "Open Settings"**
5. **Expected**: App notification settings open
6. **Enable notifications** and return to app
7. **Expected**: Notifications should now work

### **Scenario 4: Settings Management**
1. **Go to device Settings** > Apps > Appy99Lisence > Notifications
2. **Disable notifications**
3. **Return to app**
4. **Expected**: App detects disabled notifications (check logs)
5. **Re-enable notifications** in settings
6. **Return to app**
7. **Expected**: Notifications work again

### **Scenario 5: License Expiration Notifications**
1. **Activate the app** with a license
2. **Wait for expiration warnings** (or modify expiration time for testing)
3. **Expected**: Expiration warning notifications appear
4. **Check notification behavior**:
   - Non-dismissible during final 24 hours
   - Color-coded (orange/red)
   - Proper action buttons

## 🔍 **Verification Points**

### **Permission Dialog Content:**
- ✅ Clear explanation of notification benefits
- ✅ Professional UI with emojis
- ✅ "Grant Permission" and "Not Now" options
- ✅ Non-dismissible explanation dialog

### **System Integration:**
- ✅ Proper Android 13+ permission handling
- ✅ Settings app integration
- ✅ Permission state persistence
- ✅ Resume detection

### **Notification Features:**
- ✅ Test notification after permission grant
- ✅ License expiration warnings
- ✅ Proper notification channels
- ✅ Action buttons and intents

### **Error Handling:**
- ✅ Graceful permission denial
- ✅ SecurityException handling
- ✅ Settings app failure handling
- ✅ Comprehensive logging

## 🐛 **Debugging**

### **Log Tags to Monitor:**
```
adb logcat | grep "LicenseActivate"
```

### **Key Log Messages:**
- `"Requesting notification permission"`
- `"Notification permission granted"`
- `"Notification permission denied"`
- `"Test notification shown successfully"`
- `"Cannot show notification - permission not granted"`

### **Common Issues:**
1. **No permission dialog**: Check Android version (must be 13+)
2. **Notifications not showing**: Check permission status in device settings
3. **Settings not opening**: Check device compatibility
4. **Test notification fails**: Check SecurityException in logs

## 📋 **Test Checklist**

- [ ] Fresh install permission request
- [ ] Permission explanation dialog
- [ ] System permission dialog
- [ ] Permission grant success flow
- [ ] Permission denial handling
- [ ] Permanent denial redirect
- [ ] Settings app integration
- [ ] Test notification display
- [ ] License expiration notifications
- [ ] App resume permission checking
- [ ] Error handling and logging
- [ ] Multiple Android versions (if available)

## 🎉 **Expected Results**

After successful implementation:
- **Android 13+**: Full permission management with dialogs
- **Android 12 and below**: Automatic notification permissions
- **All versions**: Proper notification display and error handling
- **User experience**: Clear guidance and professional UI
- **Developer experience**: Comprehensive logging and debugging info
