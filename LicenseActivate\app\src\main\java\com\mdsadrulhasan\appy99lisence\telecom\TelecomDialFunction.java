package com.mdsadrulhasan.appy99lisence.telecom;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.preference.PreferenceManager;
import android.telecom.TelecomManager;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.os.Build;

import java.net.URLEncoder;
import java.util.List;

/**
 * Handles USSD dialing and telecom operations
 * Integrated from backup/app project into LicenseActivate
 */
public class TelecomDialFunction {
    
    private static final String TAG = "TelecomDialFunction";
    
    private Context context;
    private TelephonyManager telephonyManager;
    private SubscriptionManager subscriptionManager;
    
    public TelecomDialFunction(Context context) {
        this.context = context;
        this.telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            this.subscriptionManager = SubscriptionManager.from(context);
        }
    }
    
    /**
     * Dial USSD code
     */
    public boolean dialUp(String ussdCode, int simSlot, String orderId) {
        try {
            Log.d(TAG, "Dialing USSD: " + ussdCode + " on slot: " + simSlot + " for order: " + orderId);
            
            // Set busy flag
            setBusyFlag(true);
            
            // Save current order ID
            savePreference("main_id", orderId);
            
            // Try different dialing methods based on Android version
            boolean success = false;
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                success = dialWithTelecomManager(ussdCode, simSlot);
            }
            
            if (!success) {
                success = dialWithIntent(ussdCode, simSlot);
            }
            
            if (success) {
                Log.d(TAG, "USSD dialing initiated successfully");
            } else {
                Log.e(TAG, "Failed to initiate USSD dialing");
                setBusyFlag(false); // Clear busy flag on failure
            }
            
            return success;
            
        } catch (Exception e) {
            Log.e(TAG, "Error dialing USSD", e);
            setBusyFlag(false); // Clear busy flag on error
            return false;
        }
    }
    
    /**
     * Dial using TelecomManager (Android 5.0+)
     */
    private boolean dialWithTelecomManager(String ussdCode, int simSlot) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
                
                if (telecomManager != null) {
                    // Get subscription ID for the specified SIM slot
                    int subscriptionId = getSubscriptionId(simSlot);
                    
                    if (subscriptionId != -1) {
                        Uri uri = Uri.fromParts("tel", ussdCode, null);
                        
                        // Create call intent
                        Intent callIntent = new Intent(Intent.ACTION_CALL);
                        callIntent.setData(uri);
                        callIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        
                        // Add subscription ID for dual SIM support
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                            callIntent.putExtra("subscription", subscriptionId);
                        }
                        
                        context.startActivity(callIntent);
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error dialing with TelecomManager", e);
        }
        
        return false;
    }
    
    /**
     * Dial using Intent (fallback method)
     */
    private boolean dialWithIntent(String ussdCode, int simSlot) {
        try {
            Intent intent = new Intent(Intent.ACTION_CALL);
            intent.setData(Uri.parse("tel:" + URLEncoder.encode(ussdCode, "UTF-8")));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            // Add SIM slot information for dual SIM devices
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                int subscriptionId = getSubscriptionId(simSlot);
                if (subscriptionId != -1) {
                    intent.putExtra("subscription", subscriptionId);
                }
            }
            
            context.startActivity(intent);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error dialing with Intent", e);
            return false;
        }
    }
    
    /**
     * Get subscription ID for SIM slot
     */
    private int getSubscriptionId(int simSlot) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                
                if (subscriptionInfos != null && subscriptionInfos.size() > simSlot) {
                    return subscriptionInfos.get(simSlot).getSubscriptionId();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting subscription ID", e);
        }
        
        return -1; // Default subscription
    }
    
    /**
     * Send response to server
     */
    public void sendResponse(String orderId, String response, String status) {
        try {
            Log.d(TAG, "Sending response to server for order: " + orderId + ", status: " + status);
            
            // Create API client for sending response
            TelecomApiClient apiClient = new TelecomApiClient(context);
            apiClient.sendRechargeResponse(orderId, response, status, new TelecomApiClient.ApiCallback() {
                @Override
                public void onSuccess(String result) {
                    Log.d(TAG, "Response sent to server successfully: " + result);
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "Error sending response to server: " + error);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error sending response to server", e);
        }
    }
    
    /**
     * Check if service is busy
     */
    public boolean isBusy() {
        SharedPreferences prefs = context.getSharedPreferences("telecom_service", Context.MODE_PRIVATE);
        return prefs.getInt("busy", 0) == 1;
    }
    
    /**
     * Set busy flag
     */
    public void setBusyFlag(boolean busy) {
        SharedPreferences.Editor editor = context.getSharedPreferences("telecom_service", Context.MODE_PRIVATE).edit();
        editor.putInt("busy", busy ? 1 : 0);
        
        if (busy) {
            // Set timeout for busy flag (30 seconds)
            long timeoutTime = System.currentTimeMillis() + 30000;
            editor.putLong("busy_timeout", timeoutTime);
        } else {
            editor.remove("busy_timeout");
        }
        
        editor.apply();
        
        Log.d(TAG, "Busy flag set to: " + busy);
    }
    
    /**
     * Check and clear busy timeout
     */
    public void checkBusyTimeout() {
        SharedPreferences prefs = context.getSharedPreferences("telecom_service", Context.MODE_PRIVATE);
        
        if (prefs.getInt("busy", 0) == 1) {
            long timeoutTime = prefs.getLong("busy_timeout", 0);
            
            if (timeoutTime != 0 && System.currentTimeMillis() > timeoutTime) {
                Log.d(TAG, "Busy timeout reached, clearing busy flag");
                setBusyFlag(false);
            }
        }
    }
    
    /**
     * Get SIM operator name
     */
    public String getSimOperatorName(int simSlot) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                
                if (subscriptionInfos != null && subscriptionInfos.size() > simSlot) {
                    return subscriptionInfos.get(simSlot).getCarrierName().toString();
                }
            } else if (telephonyManager != null) {
                return telephonyManager.getSimOperatorName();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting SIM operator name", e);
        }
        
        return "Unknown";
    }
    
    /**
     * Check if SIM is available
     */
    public boolean isSimAvailable(int simSlot) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                return subscriptionInfos != null && subscriptionInfos.size() > simSlot;
            } else if (telephonyManager != null) {
                return telephonyManager.getSimState() == TelephonyManager.SIM_STATE_READY;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking SIM availability", e);
        }
        
        return false;
    }
    
    /**
     * Get number of available SIMs
     */
    public int getSimCount() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();
                return subscriptionInfos != null ? subscriptionInfos.size() : 0;
            } else {
                return 1; // Assume single SIM for older versions
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting SIM count", e);
            return 0;
        }
    }
    
    /**
     * Save preference
     */
    private void savePreference(String key, String value) {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(context).edit();
        editor.putString(key, value);
        editor.apply();
    }
    
    /**
     * Get preference
     */
    private String getPreference(String key) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(key, null);
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        // Clear any pending operations
        setBusyFlag(false);
        Log.d(TAG, "TelecomDialFunction cleaned up");
    }
}
