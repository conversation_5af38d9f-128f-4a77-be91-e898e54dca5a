[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\menu\\menu_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_nav_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\nav_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_ic_home_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\ic_home_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_toggle_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\toggle_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_modern_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\modern_status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_activity_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_modern_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\modern_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_ic_settings_cell_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\ic_settings_cell_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_fragment_telecom_monitor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\fragment_telecom_monitor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_ic_settings_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\ic_settings_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_activity_recharge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\activity_recharge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_rounded_cornerss.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\rounded_cornerss.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_item_recharge_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\item_recharge_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\xml_telecom_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\xml\\telecom_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_activity_telecom_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\activity_telecom_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_modern_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\modern_edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_telecom_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\telecom_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_rounded_corner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\rounded_corner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_modern_toggle_on.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\modern_toggle_on.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_fragment_telecom_banking.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\fragment_telecom_banking.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_ic_baseline_add_to_queue_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\ic_baseline_add_to_queue_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_activity_recharge_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\activity_recharge_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_toggle_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\toggle_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_activity_recharge_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\activity_recharge_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_modern_toggle_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\modern_toggle_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_fragment_telecom_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\fragment_telecom_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_domain_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\domain_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_modern_toggle_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\modern_toggle_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_toggle_on.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\toggle_on.xml"}, {"merged": "com.mdsadrulhasan.appy99lisence.app-release-41:/layout_activity_dashboard.xml.flat", "source": "com.mdsadrulhasan.appy99lisence.app-main-42:/layout/activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_item_telecom_log.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\item_telecom_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_footer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\footer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\layout_fragment_telecom_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\layout\\fragment_telecom_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-release-41:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mdsadrulhasan.appy99lisence.app-main-42:\\drawable\\spinner_background.xml"}]