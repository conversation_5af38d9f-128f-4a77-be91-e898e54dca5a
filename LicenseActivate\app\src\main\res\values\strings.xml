<resources>
    <string name="app_name">Appy99Lisence</string>
    <string name="telecom_accessibility_service_description">This service is required to automatically process USSD codes for mobile recharges. It reads the USSD response screens and sends the appropriate responses.</string>

    <!-- Telecom Dashboard Strings -->
    <string name="telecom_dashboard_title">Telecom Recharge System</string>
    <string name="telecom_home_tab">Home</string>
    <string name="telecom_banking_tab">Banking</string>
    <string name="telecom_settings_tab">Settings</string>
    <string name="telecom_monitor_tab">Monitor</string>

    <!-- Service Status Strings -->
    <string name="service_status_running">Service Status: Running</string>
    <string name="service_status_stopped">Service Status: Stopped</string>
    <string name="service_status_starting">Service Status: Starting...</string>
    <string name="service_status_stopping">Service Status: Stopping...</string>

    <!-- Button Strings -->
    <string name="start_service">Start Service</string>
    <string name="stop_service">Stop Service</string>
    <string name="refresh">Refresh</string>
    <string name="clear_logs">Clear Logs</string>
    <string name="export_logs">Export</string>

    <!-- Settings Strings -->
    <string name="sms_service">SMS Service</string>
    <string name="accessibility_service">Accessibility</string>
    <string name="power_optimize">Power Optimize</string>
    <string name="auto_start_service">Auto Start Service</string>
    <string name="network_settings">Network Settings</string>
    <string name="system_settings">System Settings</string>

    <!-- Banking Strings -->
    <string name="mobile_banking_services">Mobile Banking Services</string>
    <string name="banking_configuration">Banking Configuration</string>
    <string name="auto_banking">Auto Banking</string>
    <string name="banking_delay">Banking Delay (seconds)</string>
    <string name="banking_status">Banking Status</string>
    <string name="test_banking_connection">Test Banking Connection</string>

    <!-- Monitor Strings -->
    <string name="monitor_controls">Monitor Controls</string>
    <string name="live_monitor">Live Monitor</string>
    <string name="auto_scroll">Auto Scroll</string>
    <string name="no_logs_available">No logs available</string>
    <string name="no_recent_activity">No recent activity</string>

    <!-- Statistics Strings -->
    <string name="total_recharges">Total Recharges</string>
    <string name="success_rate">Success Rate</string>
    <string name="recent_activity">Recent Activity</string>

    <!-- Toast Messages -->
    <string name="telecom_service_started">Telecom service started</string>
    <string name="telecom_service_stopped">Telecom service stopped</string>
    <string name="error_starting_service">Error starting telecom service</string>
    <string name="error_stopping_service">Error stopping telecom service</string>
</resources>