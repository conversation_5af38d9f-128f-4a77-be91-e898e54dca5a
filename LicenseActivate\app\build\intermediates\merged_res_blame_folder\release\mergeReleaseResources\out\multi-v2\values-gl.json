{"logs": [{"outputFile": "com.mdsadrulhasan.appy99lisence.app-mergeReleaseResources-39:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\857a9a6b52c9eeda6cd3464bddadc08a\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,10206", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,10284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\45cfe67c7755d3e66160b5bcd91d999e\\transformed\\core-1.13.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,120", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3519,3618,3720,3820,3918,4025,4131,10529", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3613,3715,3815,3913,4020,4126,4242,10625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25f7cfac3baf7533370fea96b4f461d8\\transformed\\preference-1.2.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,276,357,495,664,752", "endColumns": "69,100,80,137,168,87,81", "endOffsets": "170,271,352,490,659,747,829"}, "to": {"startLines": "48,51,113,115,121,122,123", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4565,4775,9902,10068,10630,10799,10887", "endColumns": "69,100,80,137,168,87,81", "endOffsets": "4630,4871,9978,10201,10794,10882,10964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e6f8e0c71d36565d9ba51a46700a63c5\\transformed\\material-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1099,1177,1273,1352,1415,1510,1574,1643,1706,1780,1844,1900,2021,2079,2141,2197,2274,2413,2501,2578,2674,2758,2838,2978,3058,3138,3287,3377,3458,3514,3570,3636,3715,3796,3867,3955,4034,4111,4193,4282,4383,4467,4559,4652,4753,4827,4919,5021,5073,5157,5223,5315,5403,5465,5529,5592,5662,5773,5878,5984,6083,6143,6203,6288,6371,6450", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "270,351,431,516,618,714,819,952,1032,1094,1172,1268,1347,1410,1505,1569,1638,1701,1775,1839,1895,2016,2074,2136,2192,2269,2408,2496,2573,2669,2753,2833,2973,3053,3133,3282,3372,3453,3509,3565,3631,3710,3791,3862,3950,4029,4106,4188,4277,4378,4462,4554,4647,4748,4822,4914,5016,5068,5152,5218,5310,5398,5460,5524,5587,5657,5768,5873,5979,6078,6138,6198,6283,6366,6445,6523"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,4247,4352,4485,4635,4697,4876,4972,5051,5114,5209,5273,5342,5405,5479,5543,5599,5720,5778,5840,5896,5973,6112,6200,6277,6373,6457,6537,6677,6757,6837,6986,7076,7157,7213,7269,7335,7414,7495,7566,7654,7733,7810,7892,7981,8082,8166,8258,8351,8452,8526,8618,8720,8772,8856,8922,9014,9102,9164,9228,9291,9361,9472,9577,9683,9782,9842,9983,10289,10372,10451", "endLines": "5,33,34,35,36,37,45,46,47,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,117,118,119", "endColumns": "12,80,79,84,101,95,104,132,79,61,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,76,95,83,79,139,79,79,148,89,80,55,55,65,78,80,70,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84,82,78,77", "endOffsets": "320,3151,3231,3316,3418,3514,4347,4480,4560,4692,4770,4967,5046,5109,5204,5268,5337,5400,5474,5538,5594,5715,5773,5835,5891,5968,6107,6195,6272,6368,6452,6532,6672,6752,6832,6981,7071,7152,7208,7264,7330,7409,7490,7561,7649,7728,7805,7887,7976,8077,8161,8253,8346,8447,8521,8613,8715,8767,8851,8917,9009,9097,9159,9223,9286,9356,9467,9572,9678,9777,9837,9897,10063,10367,10446,10524"}}]}]}