<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_SMS"/>
    <uses-permission android:name="android.permission.SEND_SMS"/>
    <uses-permission android:name="android.permission.RECEIVE_SMS"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE"/>
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE"/>

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppyStoreMRecharge"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false"/>
        <activity
            android:exported="true"
            android:label="@string/app_name"
            android:name="com.appystore.mrecharge.activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <receiver android:name="com.appystore.mrecharge.IncomingSms"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.provider.Telephony.SMS_RECEIVED"/>
            </intent-filter>
        </receiver>
        <service android:name="com.appystore.mrecharge.service.PushService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <service android:name="com.appystore.mrecharge.service.Recharge"/>
        <service
            android:name="com.appystore.mrecharge.service.sever"
            android:enabled="true"
            android:exported="true"
            android:stopWithTask="false"/>
        <service android:name="com.appystore.mrecharge.service.Smsend"/>
        <service android:name="com.appystore.mrecharge.service.International_service"/>
        <service
            android:name="com.appystore.mrecharge.service.USSDService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService"/>
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/ussd_service"/>
        </service>
        <activity android:name="com.appystore.mrecharge.activity.Settings" android:exported="false"/>
        <activity android:name="com.appystore.mrecharge.activity.intsetting" android:exported="false"/>
        <activity android:name="com.appystore.mrecharge.activity.Monitoring" android:exported="false"/>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.appystore.mrecharge.androidx-startup">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"/>
        </provider>
        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false"
            android:directBootAware="false"/>
        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
            android:enabled="true"
            android:exported="false"
            android:directBootAware="false"/>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY"/>
                <action android:name="android.intent.action.BATTERY_LOW"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW"/>
                <action android:name="android.intent.action.DEVICE_STORAGE_OK"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.TIME_SET"/>
                <action android:name="android.intent.action.TIMEZONE_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS"/>
            </intent-filter>
        </receiver>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
            android:exported="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="genericidp"
                    android:host="firebase.auth"
                    android:path="/"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
            android:exported="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="recaptcha"
                    android:host="firebase.auth"
                    android:path="/"/>
            </intent-filter>
        </activity>
        <service
            android:name="com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.auth.api.gms.service.START"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </service>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false"
            android:directBootAware="true">
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
        </service>
        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:permission="com.google.android.c2dm.permission.SEND"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:exported="false"
            android:directBootAware="true">
            <intent-filter android:priority="-500">
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false">
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct"/>
        </service>
        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:exported="false"
            android:authorities="com.appystore.mrecharge.firebaseinitprovider"
            android:initOrder="100"
            android:directBootAware="true"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"/>
        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="true"
            android:exported="false"/>
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version"/>
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:exported="false"
            android:directBootAware="true"/>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="false"/>
        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false"/>


    </application>

</manifest>