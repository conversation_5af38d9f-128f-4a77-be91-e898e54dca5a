<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/activity_main"
    android:background="@drawable/bg"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:id="@+id/btnSpeakContainer"
        android:paddingTop="0dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:background="@drawable/rightorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:id="@+id/home"
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_home_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:background="@drawable/select"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:id="@+id/bank"
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_settings_cell_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="M Banking"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:background="@drawable/rightorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <ImageButton
                android:id="@+id/sms"
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:src="@drawable/ic_settings_black_24dp"
                android:scaleType="fitCenter"/>
            <TextView
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"/>
        </LinearLayout>
    </LinearLayout>
    <ScrollView
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="1dp"
        android:layout_marginRight="1dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_gravity="center"
                android:orientation="vertical"
                android:background="@drawable/rounded_corner"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:layout_alignParentLeft="true">
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#ff2cab0e"
                    android:id="@+id/gpt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="GP"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/gp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter USSD"
                    android:inputType="number"/>
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#ff2cab0e"
                    android:id="@+id/blt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="ROBI"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/robi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter USSD"
                    android:inputType="number"/>
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#ff2cab0e"
                    android:id="@+id/sima_rocketj"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="AIRTEL"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/at"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter USSD"
                    android:inputType="number"/>
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#ff2cab0e"
                    android:id="@+id/sima_rocketm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="BANGLALINK"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/bl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter USSD"
                    android:inputType="number"/>
                <TextView
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:textColor="#ff2cab0e"
                    android:id="@+id/ttt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="TELETALK"/>
                <EditText
                    android:textSize="16sp"
                    android:textColor="#ff3c3adb"
                    android:textColorHint="#ff6722e0"
                    android:id="@+id/tt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:hint="Enter USSD"
                    android:inputType="number"/>
                <Button
                    android:textColor="#fff7f0ef"
                    android:layout_gravity="right"
                    android:id="@+id/save"
                    android:background="@drawable/save"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:layout_marginRight="20dp"
                    android:text="Save"
                    android:layout_alignParentRight="true"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>