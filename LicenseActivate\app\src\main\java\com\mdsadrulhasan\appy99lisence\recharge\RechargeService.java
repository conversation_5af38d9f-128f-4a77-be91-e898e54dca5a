package com.mdsadrulhasan.appy99lisence.recharge;

import android.app.Service;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.Nullable;

/**
 * Background service for processing recharge requests
 * Handles automatic USSD dialing and status monitoring
 */
public class RechargeService extends Service {
    
    private static final String TAG = "RechargeService";
    private static final int DEFAULT_CHECK_INTERVAL = 5000; // 5 seconds
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    private Handler handler;
    private Runnable processingRunnable;
    private RechargeDbHelper dbHelper;
    private USSDDialer ussdDialer;
    private SharedPreferences preferences;
    private RechargeApiClient apiClient;
    
    private boolean isServiceRunning = false;
    private boolean isProcessingBusy = false;
    private int checkInterval = DEFAULT_CHECK_INTERVAL;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "RechargeService onCreate");
        
        // Initialize components
        handler = new Handler(Looper.getMainLooper());
        dbHelper = new RechargeDbHelper(this);
        ussdDialer = new USSDDialer(this);
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        apiClient = new RechargeApiClient(this);
        
        // Get check interval from preferences
        checkInterval = Integer.parseInt(getPreference("recharge_check_interval", "5")) * 1000;
        
        setupProcessingTask();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "RechargeService onStartCommand");
        
        if (!isServiceRunning) {
            startProcessing();
        }
        
        // Return START_STICKY to restart service if killed
        return START_STICKY;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "RechargeService onDestroy");
        
        stopProcessing();
        cleanup();
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // This is a started service, not bound
    }
    
    private void setupProcessingTask() {
        processingRunnable = new Runnable() {
            @Override
            public void run() {
                if (isServiceRunning && isRechargeEnabled()) {
                    processRechargeRequests();
                    processSmsResponses();
                    syncWithServer();
                }
                
                // Schedule next run
                if (isServiceRunning) {
                    handler.postDelayed(this, checkInterval);
                }
            }
        };
    }
    
    private void startProcessing() {
        if (isServiceRunning) {
            return;
        }
        
        isServiceRunning = true;
        Log.d(TAG, "Starting recharge processing");
        
        // Start the processing loop
        handler.post(processingRunnable);
        
        Toast.makeText(this, "Recharge service started", Toast.LENGTH_SHORT).show();
    }
    
    private void stopProcessing() {
        if (!isServiceRunning) {
            return;
        }
        
        isServiceRunning = false;
        Log.d(TAG, "Stopping recharge processing");
        
        // Remove pending callbacks
        if (handler != null && processingRunnable != null) {
            handler.removeCallbacks(processingRunnable);
        }
        
        Toast.makeText(this, "Recharge service stopped", Toast.LENGTH_SHORT).show();
    }
    
    private void processRechargeRequests() {
        if (isProcessingBusy) {
            Log.d(TAG, "Service is busy, skipping recharge processing");
            return;
        }
        
        try {
            Cursor cursor = dbHelper.getPendingRecharges();
            
            if (cursor != null && cursor.moveToFirst()) {
                // Set busy flag
                isProcessingBusy = true;
                savePreference("recharge_service_busy", "1");
                
                // Process the first pending recharge
                String orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));
                String ussdCode = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_USSD_CODE));
                int simSlot = cursor.getInt(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SIM_SLOT));
                
                Log.d(TAG, "Processing recharge order: " + orderId);
                
                // Execute USSD code
                ussdDialer.executeRecharge(ussdCode, simSlot, orderId);
                
                // Set timeout for busy flag (30 seconds)
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        isProcessingBusy = false;
                        savePreference("recharge_service_busy", "0");
                        Log.d(TAG, "Busy timeout reached, resetting busy flag");
                    }
                }, 30000);
                
            } else {
                Log.d(TAG, "No pending recharge requests found");
            }
            
            if (cursor != null) {
                cursor.close();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing recharge requests", e);
            isProcessingBusy = false;
            savePreference("recharge_service_busy", "0");
        }
    }
    
    private void processSmsResponses() {
        try {
            Cursor cursor = dbHelper.getUnprocessedSms();
            
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    long smsId = cursor.getLong(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ID));
                    String sender = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_SENDER));
                    String messageBody = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_MESSAGE_BODY));
                    String orderId = cursor.getString(cursor.getColumnIndex(RechargeDbHelper.COLUMN_ORDER_ID));
                    
                    Log.d(TAG, "Processing SMS from: " + sender + ", Order: " + orderId);
                    
                    // Process SMS response
                    if (processSmsResponse(sender, messageBody, orderId)) {
                        // Mark as processed
                        dbHelper.markSmsAsProcessed(smsId);
                        
                        // Reset busy flag if this SMS corresponds to current processing
                        if (orderId != null && !orderId.isEmpty()) {
                            isProcessingBusy = false;
                            savePreference("recharge_service_busy", "0");
                        }
                    }
                    
                } while (cursor.moveToNext());
            }
            
            if (cursor != null) {
                cursor.close();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS responses", e);
        }
    }
    
    private boolean processSmsResponse(String sender, String messageBody, String orderId) {
        try {
            // Determine if SMS indicates success or failure
            String status = "completed";
            if (messageBody.toLowerCase().contains("failed") || 
                messageBody.toLowerCase().contains("error") ||
                messageBody.toLowerCase().contains("insufficient")) {
                status = "failed";
            }
            
            // Update recharge status
            if (orderId != null && !orderId.isEmpty()) {
                dbHelper.updateRechargeStatus(orderId, status, null, messageBody);
                Log.d(TAG, "Updated recharge status for order " + orderId + " to " + status);
            } else {
                // Try to match SMS with pending recharge based on content
                // This is a fallback when order ID is not available
                Log.d(TAG, "No order ID in SMS, attempting to match with pending recharge");
            }
            
            // Send SMS response to server
            apiClient.processSmsResponse(sender, messageBody, orderId);
            
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS response", e);
            return false;
        }
    }
    
    private void syncWithServer() {
        try {
            // Sync pending recharges with server
            // This will be implemented in RechargeApiClient
            apiClient.syncRechargeStatus();
            
        } catch (Exception e) {
            Log.e(TAG, "Error syncing with server", e);
        }
    }
    
    private boolean isRechargeEnabled() {
        return getPreference("recharge_service_enabled", "1").equals("1");
    }
    
    private String getPreference(String key, String defaultValue) {
        return preferences.getString(key, defaultValue);
    }
    
    private void savePreference(String key, String value) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, value);
        editor.apply();
    }
    
    private void cleanup() {
        if (ussdDialer != null) {
            ussdDialer.cleanup();
        }
        
        if (dbHelper != null) {
            dbHelper.close();
        }
        
        if (apiClient != null) {
            apiClient.cleanup();
        }
    }
    
    // Public methods for external control
    public static void startRechargeService(android.content.Context context) {
        Intent intent = new Intent(context, RechargeService.class);
        context.startService(intent);
    }
    
    public static void stopRechargeService(android.content.Context context) {
        Intent intent = new Intent(context, RechargeService.class);
        context.stopService(intent);
    }
}
