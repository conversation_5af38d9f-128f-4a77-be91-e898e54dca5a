package com.mdsadrulhasan.appy99lisence.recharge;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Parcelable;
import android.telecom.PhoneAccountHandle;
import android.telecom.TelecomManager;
import android.util.Log;
import android.widget.Toast;

import java.lang.reflect.Method;
import java.util.List;

/**
 * USSD Dialer utility for executing recharge USSD codes
 * Handles dual-SIM support and various device manufacturers
 */
public class USSDDialer {

    private static final String TAG = "USSDDialer";
    private Context context;
    private RechargeDbHelper dbHelper;

    public USSDDialer(Context context) {
        this.context = context;
        this.dbHelper = new RechargeDbHelper(context);
    }

    /**
     * Execute USSD code for recharge
     * @param ussdCode The USSD code to dial (e.g., *121*50************#)
     * @param simSlot SIM slot to use (1 or 2)
     * @param orderId Order ID for tracking
     */
    public void executeRecharge(String ussdCode, int simSlot, String orderId) {
        Log.d(TAG, "Executing recharge USSD: " + ussdCode + " on SIM slot: " + simSlot + " for order: " + orderId);

        try {
            // Update database status to processing
            dbHelper.updateRechargeStatus(orderId, "processing", null, null);

            // Try different dialing methods based on device capabilities
            boolean success = false;

            // Method 1: Try TelecomManager approach (Android 5.0+)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                success = dialWithTelecomManager(ussdCode, simSlot);
            }

            // Method 2: Fallback to intent-based dialing
            if (!success) {
                success = dialWithIntent(ussdCode, simSlot);
            }

            if (success) {
                Toast.makeText(context, "Dialing recharge code for order: " + orderId, Toast.LENGTH_SHORT).show();
                Log.d(TAG, "Successfully initiated USSD dial for order: " + orderId);
            } else {
                // Update status to failed
                dbHelper.updateRechargeStatus(orderId, "failed", "Failed to initiate USSD dial", null);
                Toast.makeText(context, "Failed to dial USSD code", Toast.LENGTH_SHORT).show();
                Log.e(TAG, "Failed to initiate USSD dial for order: " + orderId);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error executing recharge USSD", e);
            dbHelper.updateRechargeStatus(orderId, "failed", "Exception: " + e.getMessage(), null);
            Toast.makeText(context, "Error executing recharge: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Dial USSD using TelecomManager (Android 5.0+)
     */
    private boolean dialWithTelecomManager(String ussdCode, int simSlot) {
        try {
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) {
                return false;
            }

            TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
            if (telecomManager == null) {
                Log.w(TAG, "TelecomManager not available");
                return false;
            }

            List<PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();
            if (phoneAccounts == null || phoneAccounts.isEmpty()) {
                Log.w(TAG, "No phone accounts available");
                return false;
            }

            // Prepare USSD URI
            String encodedHash = Uri.encode("#");
            String ussdUri = "tel:" + ussdCode.replace("#", encodedHash);

            Intent callIntent = new Intent(Intent.ACTION_CALL);
            callIntent.setData(Uri.parse(ussdUri));
            callIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            // Select appropriate SIM slot
            if (simSlot > 0 && simSlot <= phoneAccounts.size()) {
                PhoneAccountHandle phoneAccount = phoneAccounts.get(simSlot - 1);
                callIntent.putExtra("android.telecom.extra.PHONE_ACCOUNT_HANDLE", phoneAccount);
                Log.d(TAG, "Using phone account: " + phoneAccount.toString());
            } else {
                Log.w(TAG, "Invalid SIM slot or no phone account available for slot: " + simSlot);
                // Use default phone account
                if (!phoneAccounts.isEmpty()) {
                    callIntent.putExtra("android.telecom.extra.PHONE_ACCOUNT_HANDLE", phoneAccounts.get(0));
                }
            }

            context.startActivity(callIntent);
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error with TelecomManager dialing", e);
            return false;
        }
    }

    /**
     * Dial USSD using Intent with manufacturer-specific extras
     */
    private boolean dialWithIntent(String ussdCode, int simSlot) {
        try {
            String encodedHash = Uri.encode("#");
            String ussdUri = "tel:" + ussdCode.replace("#", encodedHash);

            Intent callIntent = new Intent(Intent.ACTION_CALL);
            callIntent.setData(Uri.parse(ussdUri));
            callIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            // Add manufacturer-specific extras for dual-SIM support
            addDualSimExtras(callIntent, simSlot);

            context.startActivity(callIntent);
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error with Intent dialing", e);
            return false;
        }
    }

    /**
     * Add manufacturer-specific extras for dual-SIM support
     */
    private void addDualSimExtras(Intent intent, int simSlot) {
        // Convert to 0-based index for most manufacturers
        int slotIndex = simSlot - 1;

        // Common dual-SIM extras for various manufacturers
        String[] simSlotKeys = {
            // Samsung
            "com.android.phone.extra.slot",
            "slot",
            "simslot",
            "sim_slot",
            "Subscription",
            "subscription",

            // HTC
            "com.htc.phone.extra.slot",

            // Motorola
            "com.motorola.android.intent.extra.SLOT_ID",

            // Sony
            "com.sonyericsson.phone.extra.SLOT_ID",

            // ASUS
            "extra_asus_dial_use_dualsim",

            // Xiaomi/MIUI
            "phone",
            "com.android.phone.DialingMode",

            // Generic
            "simSlot",
            "slot_id",
            "simId",
            "simnum",
            "phone_type",
            "slotId",
            "slotIdx"
        };

        // Add all possible extras
        for (String key : simSlotKeys) {
            intent.putExtra(key, slotIndex);
        }

        // Additional extras
        intent.putExtra("com.android.phone.force.slot", true);
        intent.putExtra("Cdma_Supp", true);

        Log.d(TAG, "Added dual-SIM extras for slot: " + simSlot + " (index: " + slotIndex + ")");
    }

    /**
     * Get available phone accounts using reflection for compatibility
     */
    @SuppressWarnings("unchecked")
    private List<PhoneAccountHandle> getPhoneAccountsReflection() {
        try {
            Class<?> telecomManagerClass = Class.forName("android.telecom.TelecomManager");
            Method fromMethod = telecomManagerClass.getMethod("from", Context.class);
            Object telecomManager = fromMethod.invoke(null, context);

            Method getAccountsMethod = telecomManagerClass.getMethod("getCallCapablePhoneAccounts");
            return (List<PhoneAccountHandle>) getAccountsMethod.invoke(telecomManager);

        } catch (Exception e) {
            Log.e(TAG, "Error getting phone accounts via reflection", e);
            return null;
        }
    }

    /**
     * Check if device supports dual-SIM
     */
    public boolean isDualSimDevice() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
                if (telecomManager != null) {
                    List<PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();
                    return phoneAccounts != null && phoneAccounts.size() > 1;
                }
            }

            // Fallback: Check using reflection
            List<PhoneAccountHandle> accounts = getPhoneAccountsReflection();
            return accounts != null && accounts.size() > 1;

        } catch (Exception e) {
            Log.e(TAG, "Error checking dual-SIM support", e);
            return false;
        }
    }

    /**
     * Get number of available SIM slots
     */
    public int getSimSlotCount() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
                if (telecomManager != null) {
                    List<PhoneAccountHandle> phoneAccounts = telecomManager.getCallCapablePhoneAccounts();
                    return phoneAccounts != null ? phoneAccounts.size() : 1;
                }
            }

            // Fallback
            List<PhoneAccountHandle> accounts = getPhoneAccountsReflection();
            return accounts != null ? accounts.size() : 1;

        } catch (Exception e) {
            Log.e(TAG, "Error getting SIM slot count", e);
            return 1; // Default to single SIM
        }
    }

    /**
     * Generate USSD code from pattern
     */
    public String generateUssdCode(String pattern, String phoneNumber, int amount) {
        if (pattern == null || phoneNumber == null) {
            return null;
        }

        return pattern.replace("{number}", phoneNumber).replace("{amount}", String.valueOf(amount));
    }

    /**
     * Validate USSD code format
     */
    public boolean isValidUssdCode(String ussdCode) {
        if (ussdCode == null || ussdCode.trim().isEmpty()) {
            return false;
        }

        // Basic USSD validation: should start with * and end with #
        return ussdCode.startsWith("*") && ussdCode.endsWith("#") && ussdCode.length() > 2;
    }

    /**
     * Clean up resources
     */
    public void cleanup() {
        if (dbHelper != null) {
            dbHelper.close();
        }
    }
}
