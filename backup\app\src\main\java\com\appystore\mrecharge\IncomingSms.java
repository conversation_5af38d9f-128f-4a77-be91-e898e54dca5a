package com.appystore.mrecharge;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.telephony.SmsManager;
import android.telephony.SmsMessage;

public class IncomingSms extends BroadcastReceiver {
    private DbHelper mydb;
    final SmsManager sms = SmsManager.getDefault();

    public void onReceive(Context context, Intent intent) {
        this.mydb = new DbHelper(context);
        Bundle extras = intent.getExtras();
        if (extras != null) {
            Object[] objArr = (Object[]) extras.get("pdus");
            SmsMessage[] smsMessageArr = new SmsMessage[objArr.length];
            String str = "";
            String str2 = str;
            for (int i = 0; i < smsMessageArr.length; i++) {
                SmsMessage createFromPdu = SmsMessage.createFromPdu((byte[]) objArr[i]);
                String str3 = createFromPdu.getMessageBody().toString();
                str2 = createFromPdu.getOriginatingAddress();
                str = str + str3 + "";
            }
            this.mydb.insertSMS(str, str2, "0");
        }
    }
}
