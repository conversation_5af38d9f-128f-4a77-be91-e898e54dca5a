<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_blue">#4A5DC7</color>
    <color name="background_light">#EEEEEE</color>
    <color name="black">#FF000000</color>
    <color name="card_background">#FFFFFF</color>
    <color name="colorPrimary">#6200EE</color>
    <color name="done">#00CC00</color>
    <color name="error_red">#F44336</color>
    <color name="failed">#FF0000</color>
    <color name="primary_blue">#3F51B5</color>
    <color name="primary_blue_dark">#1A237E</color>
    <color name="primary_blue_light">#3949AB</color>
    <color name="selected_tab_color">#4A5DC7</color>
    <color name="success_green">#4CAF50</color>
    <color name="text_hint">#9E9E9E</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#666666</color>
    <color name="waiting">#FFAA00</color>
    <color name="warning_orange">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="accessibility_service">Accessibility</string>
    <string name="app_name">Appy99Lisence</string>
    <string name="auto_banking">Auto Banking</string>
    <string name="auto_scroll">Auto Scroll</string>
    <string name="auto_start_service">Auto Start Service</string>
    <string name="banking_configuration">Banking Configuration</string>
    <string name="banking_delay">Banking Delay (seconds)</string>
    <string name="banking_status">Banking Status</string>
    <string name="clear_logs">Clear Logs</string>
    <string name="error_starting_service">Error starting telecom service</string>
    <string name="error_stopping_service">Error stopping telecom service</string>
    <string name="export_logs">Export</string>
    <string name="live_monitor">Live Monitor</string>
    <string name="mobile_banking_services">Mobile Banking Services</string>
    <string name="monitor_controls">Monitor Controls</string>
    <string name="network_settings">Network Settings</string>
    <string name="no_logs_available">No logs available</string>
    <string name="no_recent_activity">No recent activity</string>
    <string name="power_optimize">Power Optimize</string>
    <string name="recent_activity">Recent Activity</string>
    <string name="refresh">Refresh</string>
    <string name="service_status_running">Service Status: Running</string>
    <string name="service_status_starting">Service Status: Starting...</string>
    <string name="service_status_stopped">Service Status: Stopped</string>
    <string name="service_status_stopping">Service Status: Stopping...</string>
    <string name="sms_service">SMS Service</string>
    <string name="start_service">Start Service</string>
    <string name="stop_service">Stop Service</string>
    <string name="success_rate">Success Rate</string>
    <string name="system_settings">System Settings</string>
    <string name="telecom_accessibility_service_description">This service is required to automatically process USSD codes for mobile recharges. It reads the USSD response screens and sends the appropriate responses.</string>
    <string name="telecom_banking_tab">Banking</string>
    <string name="telecom_dashboard_title">Telecom Recharge System</string>
    <string name="telecom_home_tab">Home</string>
    <string name="telecom_monitor_tab">Monitor</string>
    <string name="telecom_service_started">Telecom service started</string>
    <string name="telecom_service_stopped">Telecom service stopped</string>
    <string name="telecom_settings_tab">Settings</string>
    <string name="test_banking_connection">Test Banking Connection</string>
    <string name="total_recharges">Total Recharges</string>
    <style name="Base.Theme.Appy99Lisence" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="Theme.Appy99Lisence" parent="Base.Theme.Appy99Lisence"/>
</resources>